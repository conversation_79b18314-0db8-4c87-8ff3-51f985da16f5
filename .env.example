# Server Configuration
PORT=3000
NODE_ENV=development

# LLM Configuration
LLM_PROVIDER=openai  # Options: openai, azure-openai, anthropic, local
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-3.5-turbo
ANTHROPIC_API_KEY=your_anthropic_api_key
ANTHROPIC_MODEL=claude-2
LOCAL_LLM_URL=http://localhost:8080

# Azure OpenAI Configuration
OPENAI_CLIENT_ID=your_client_id
OPENAI_CLIENT_SECRET=your_client_secret
OPENAI_APP_KEY=your_app_key
OPENAI_TOKEN_URL=https://id.cisco.com/oauth2/default/v1/token
AZURE_OPENAI_ENDPOINT=https://chat-ai.cisco.com
OPENAI_API_VERSION=2023-08-01-preview
AZURE_OPENAI_DEPLOYMENT=gpt-4o

# Embedding Service Configuration
EMBEDDING_PROVIDER=openai  # Options: openai, huggingface, local
OPENAI_EMBEDDING_MODEL=text-embedding-ada-002
HUGGINGFACE_EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2

# Vector Database Configuration
VECTOR_DB_PROVIDER=qdrant  # Options: qdrant, pinecone, milvus
QDRANT_URL=http://localhost:6333
QDRANT_COLLECTION=documents
VECTOR_DIMENSION=1536  # Depends on the embedding model

# Document Processing
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
SKIP_DOCUMENT_PROCESSING=false  # Set to true to skip document processing on startup
