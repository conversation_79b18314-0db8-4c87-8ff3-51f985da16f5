# BI ChatAgent

A modular and configurable chatbot agent for answering questions about Tableau and Business Objects using natural language processing.

## Features

- **Document Processing**: Automatically processes .docx files and stores them as vector embeddings
- **Semantic Search**: Finds relevant information using vector similarity search
- **Natural Language Understanding**: Uses LLMs to generate context-aware responses
- **Floating Chat Widget**: Non-intrusive UI that can be integrated into any web application
- **Modular Architecture**: Support for multiple LLM providers, embedding services, and vector databases
- **Configurable LLM providers**: OpenAI, Azure OpenAI, Anthropic
- **Configurable embedding services**: OpenAI, HuggingFace
- **Vector database integration**: Qdrant running in Podman
- **Azure OpenAI Integration**: Support for Azure OpenAI with Cisco OAuth token-based authentication

## Architecture

### System Architecture

```mermaid
graph TD
    subgraph "Frontend"
        A[React App] --> B[Chat Widget]
    end

    subgraph "Backend"
        C[Express Server] --> D[Chat Controller]
        C --> E[Document Controller]

        D --> F[LLM Service]
        D --> G[Embedding Service]
        D --> H[Vector DB Service]

        E --> I[Document Processor]
        I --> G
        I --> H
    end

    subgraph "Storage"
        J[Qdrant Vector DB]
        K[Document Files]
    end

    B <--> C
    H <--> J
    I <--> K
```

### Chat Process Flow

```mermaid
flowchart TD
    A[User Sends Message] --> B[Frontend Sends to Backend]
    B --> C[Generate Query Embedding]
    C --> D[Search Vector Database]
    D --> E[Retrieve Relevant Documents]
    E --> F[Generate Response with LLM]
    F --> G[Return Response to Frontend]
    G --> H[Display Response to User]
```

### Document Processing Flow

```mermaid
flowchart TD
    A[Start Server] --> B[Find Documents]
    B --> C[Process Each Document]
    C --> D[Extract Text]
    D --> E[Split into Chunks]
    E --> F[Generate Embeddings]
    F --> G[Store in Vector Database]
    G --> H[Server Ready for Queries]
```

## Prerequisites

- Node.js (v14 or higher)
- Podman Desktop (for running Qdrant)
- API keys for LLM providers (OpenAI, Anthropic)

## Installation

### Backend Setup

1. Clone the repository:
   ```bash
   git clone https://www-github.cisco.com/DATA-PLATFORMS/BI-ChatAgent.git
   cd BI-ChatAgent
   ```

2. Install backend dependencies:
   ```bash
   npm install
   ```

3. Copy `.env.example` to `.env` and update the configuration:
   ```bash
   cp .env.example .env
   # Edit .env with your preferred text editor
   ```

4. Start the Qdrant vector database:
   ```bash
   ./setup-qdrant-podman.sh
   ```

5. Start the backend server:
   ```bash
   npm run dev
   ```

### Frontend Setup

1. Navigate to the client directory:
   ```bash
   cd client
   ```

2. Install frontend dependencies:
   ```bash
   npm install
   ```

3. Create a `.env` file for the frontend:
   ```bash
   echo "VITE_API_URL=http://localhost:3000/api" > .env
   ```

4. Start the frontend development server:
   ```bash
   npm run dev
   ```

5. Open your browser to the URL shown in the terminal (usually http://localhost:5173)

## Configuration

The application is configured using environment variables in the `.env` file:

- `PORT`: Server port (default: 3000)
- `NODE_ENV`: Environment (development, production)
- `LLM_PROVIDER`: LLM provider (openai, azure-openai, anthropic, local)
- `EMBEDDING_PROVIDER`: Embedding provider (openai, huggingface, local)
- `VECTOR_DB_PROVIDER`: Vector database provider (qdrant)
- `CHUNK_SIZE`: Document chunk size (default: 1000)
- `CHUNK_OVERLAP`: Document chunk overlap (default: 200)

See `.env.example` for all available configuration options.

### Azure OpenAI Configuration

To use Azure OpenAI with Cisco OAuth token-based authentication:

1. Set `LLM_PROVIDER=azure-openai` in your `.env` file
2. Configure the following Azure OpenAI settings:
   - `OPENAI_CLIENT_ID`: Your Cisco OAuth client ID
   - `OPENAI_CLIENT_SECRET`: Your Cisco OAuth client secret
   - `OPENAI_APP_KEY`: Your app key (e.g., 'egai-prd-IT-20071036-1188-other')
   - `OPENAI_TOKEN_URL`: Cisco OAuth token URL (default: 'https://id.cisco.com/oauth2/default/v1/token')
   - `AZURE_OPENAI_ENDPOINT`: Azure OpenAI endpoint (default: 'https://chat-ai.cisco.com')
   - `OPENAI_API_VERSION`: API version (default: '2023-08-01-preview')
   - `AZURE_OPENAI_DEPLOYMENT`: Deployment name (e.g., 'gpt-4o')

## API Endpoints

### Chat

```
POST /api/chat
```

Request body:
```json
{
  "message": "What is the main topic of the documents?",
  "conversationId": "optional-conversation-id"
}
```

Response:
```json
{
  "response": "The main topic of the documents is...",
  "conversationId": "conversation-id",
  "sources": [
    {
      "id": "document-id",
      "score": 0.95,
      "metadata": {
        "fileName": "document.docx",
        "chunkIndex": 0
      }
    }
  ]
}
```

### Upload Document

```
POST /api/documents/upload
```

Request body:
```json
{
  "filePath": "/path/to/document.docx"
}
```

Response:
```json
{
  "message": "Document processed successfully",
  "documentCount": 10,
  "metadata": {
    "fileName": "document.docx",
    "filePath": "/path/to/document.docx",
    "fileType": ".docx",
    "processedAt": "2023-01-01T00:00:00.000Z",
    "chunkCount": 10
  }
}
```

### List Documents

```
GET /api/documents
```

Response:
```json
{
  "documents": [
    {
      "id": "document-id",
      "text": "Document text...",
      "metadata": {
        "fileName": "document.docx",
        "chunkIndex": 0
      }
    }
  ],
  "count": 1
}
```

### Delete Document

```
DELETE /api/documents/:id
```

Response:
```json
{
  "message": "Document deleted successfully",
  "id": "document-id"
}
```

## Frontend Features

The frontend is built with React and Vite, providing a modern and responsive user interface:

- **Floating Chat Widget**: A non-intrusive chat button that expands into a full chat interface
- **Markdown Support**: Rich text formatting in chat messages
- **Source Attribution**: Shows the source documents for each response
- **Responsive Design**: Works on desktop and mobile devices
- **Real-time Feedback**: Visual indicators for loading and errors

## Usage

### Adding Documents

1. Place your .docx files in the `docs` folder
2. Restart the server to automatically process the documents
3. The documents will be chunked, embedded, and stored in the vector database

### Interacting with the Chatbot

1. Open the frontend application in your browser
2. Click the floating chat button in the bottom right corner
3. Type your question about Tableau or Business Objects
4. View the response with source attribution
5. Continue the conversation with follow-up questions

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

MIT
