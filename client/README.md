# Chatbot Agent Frontend

A React-based frontend for the Chatbot Agent that allows users to interact with the backend server through a chat interface.

## Features

- Clean, modern UI with React and Vite
- Real-time chat interface
- Markdown support for messages
- Source attribution for responses
- Server connection status indicator

## Prerequisites

- Node.js (v14 or higher)
- npm or yarn
- Backend server running on http://localhost:3000

## Installation

1. Install dependencies:
   ```
   npm install
   ```

2. Configure the environment:
   - Create a `.env` file in the client directory
   - Set `VITE_API_URL=http://localhost:3000/api` (or your backend URL)

3. Start the development server:
   ```
   npm run dev
   ```

4. Build for production:
   ```
   npm run build
   ```

## Usage

1. Start the backend server first
2. Start the frontend development server
3. Open your browser to the URL shown in the terminal (usually http://localhost:5173)
4. Start chatting with the agent!

## Project Structure

- `src/components/` - React components
- `src/services/` - API services
- `src/assets/` - Static assets
- `.env` - Environment configuration

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run lint` - Run ESLint
- `npm run preview` - Preview production build locally
