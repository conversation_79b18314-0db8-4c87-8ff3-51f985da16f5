:root {
  --primary-color: #4a6fa5;
  --secondary-color: #166088;
  --accent-color: #4fc3f7;
  --background-color: #f5f7fa;
  --text-color: #333;
  --light-gray: #e0e0e0;
  --dark-gray: #757575;
  --success-color: #4caf50;
  --error-color: #f44336;
  --border-radius: 8px;
  --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  --chat-width: 450px;
  --chat-height: 650px;
  --chat-button-size: 60px;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: var(--background-color);
  color: var(--text-color);
  line-height: 1.6;
}

#root {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  position: relative;
}

.demo-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

.demo-content h1 {
  margin-bottom: 1rem;
  color: var(--primary-color);
}

.demo-content p {
  font-size: 1.1rem;
  color: var(--dark-gray);
}
