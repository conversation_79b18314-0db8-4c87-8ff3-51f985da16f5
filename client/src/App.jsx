import { useState } from 'react';
import './App.css';
import ChatWidget from './components/ChatWidget';

function App() {
  const [isChatOpen, setIsChatOpen] = useState(false);

  const toggleChat = () => {
    setIsChatOpen(!isChatOpen);
  };

  return (
    <div className="app-container">
      <div className="demo-content">
        <h1>Your Main Application</h1>
        <p>This is your main application content. The chatbot is available via the floating button in the bottom right corner.</p>
      </div>

      <ChatWidget isOpen={isChatOpen} toggleChat={toggleChat} />
    </div>
  );
}

export default App;
