.chat-interface {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
}

.chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: white;
  overflow: hidden;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

.input-container {
  display: flex;
  padding: 1rem;
  border-top: 1px solid var(--light-gray);
  background-color: white;
}

.input-container input {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: 1rem;
  outline: none;
  transition: border-color 0.3s;
}

.input-container input:focus {
  border-color: var(--accent-color);
}

.input-container button {
  margin-left: 0.5rem;
  padding: 0.75rem 1.5rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.3s;
}

.input-container button:hover {
  background-color: var(--secondary-color);
}

.input-container button:disabled {
  background-color: var(--light-gray);
  cursor: not-allowed;
}

.error-message {
  padding: 0.75rem;
  margin: 0.5rem 0;
  background-color: rgba(244, 67, 54, 0.1);
  color: var(--error-color);
  border-radius: var(--border-radius);
}

/* Typing indicator */
.typing-indicator {
  display: flex;
  align-items: center;
}

.typing-indicator span {
  height: 8px;
  width: 8px;
  margin: 0 2px;
  background-color: var(--dark-gray);
  border-radius: 50%;
  display: inline-block;
  animation: bounce 1.5s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: 0s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes bounce {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-5px);
  }
}
