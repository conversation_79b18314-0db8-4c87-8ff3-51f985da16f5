.chat-widget {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
}

.chat-widget-button {
  width: var(--chat-button-size);
  height: var(--chat-button-size);
  border-radius: 50%;
  background-color: var(--primary-color);
  color: white;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.chat-widget-button:hover {
  background-color: var(--secondary-color);
  transform: scale(1.05);
}

.chat-widget-button.active {
  background-color: var(--secondary-color);
}

.chat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.chat-widget-container {
  position: absolute;
  bottom: calc(var(--chat-button-size) + 20px);
  right: 0;
  width: var(--chat-width);
  height: var(--chat-height);
  background-color: white;
  border-radius: var(--border-radius);
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: slideIn 0.3s ease-out;
  resize: both;
  max-width: 90vw;
  max-height: 90vh;
}

.chat-widget-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  background-color: var(--primary-color);
  color: white;
}

.chat-widget-header h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 500;
}

.header-buttons {
  display: flex;
  align-items: center;
}

.close-button, .maximize-button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  padding: 0;
  margin-left: 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.close-button {
  font-size: 1.5rem;
  line-height: 1;
}

.close-button:hover, .maximize-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.chat-widget-body {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Resize handle */
.resize-handle {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 20px;
  height: 20px;
  cursor: nwse-resize;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.resize-handle svg {
  fill: var(--dark-gray);
  opacity: 0.5;
  transition: opacity 0.2s ease;
}

.resize-handle:hover svg {
  opacity: 1;
}

/* Media queries for mobile responsiveness */
@media (max-width: 600px) {
  .chat-widget-container {
    width: calc(100vw - 40px);
    height: 75vh;
    bottom: calc(var(--chat-button-size) + 20px);
    right: 0;
  }
}
