import React, { useState, useRef, useEffect } from 'react';
import ChatInterface from './ChatInterface';
import './ChatWidget.css';

const ChatWidget = ({ isOpen, toggleChat }) => {
  const [size, setSize] = useState({ width: 0, height: 0 });
  const [isResizing, setIsResizing] = useState(false);
  const [isMaximized, setIsMaximized] = useState(false);
  const containerRef = useRef(null);
  const initialPos = useRef({ x: 0, y: 0 });
  const initialSize = useRef({ width: 0, height: 0 });
  const preMaximizeSize = useRef({ width: 0, height: 0 });

  // Initialize size from CSS variables when component mounts
  useEffect(() => {
    if (isOpen && containerRef.current) {
      const computedStyle = getComputedStyle(document.documentElement);
      const defaultWidth = parseInt(computedStyle.getPropertyValue('--chat-width'));
      const defaultHeight = parseInt(computedStyle.getPropertyValue('--chat-height'));

      // Only set initial size if not already set
      if (size.width === 0 && size.height === 0) {
        setSize({ width: defaultWidth, height: defaultHeight });
      }

      // Apply size to container
      containerRef.current.style.width = `${size.width || defaultWidth}px`;
      containerRef.current.style.height = `${size.height || defaultHeight}px`;
    }
  }, [isOpen, size]);

  const handleMouseDown = (e) => {
    e.preventDefault();
    setIsResizing(true);
    initialPos.current = { x: e.clientX, y: e.clientY };
    initialSize.current = { ...size };

    // Add event listeners for resize
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  const handleMouseMove = (e) => {
    if (isResizing && containerRef.current) {
      // Calculate new size
      const deltaX = e.clientX - initialPos.current.x;
      const deltaY = e.clientY - initialPos.current.y;

      const newWidth = Math.max(300, initialSize.current.width + deltaX);
      const newHeight = Math.max(400, initialSize.current.height + deltaY);

      // Update container size
      containerRef.current.style.width = `${newWidth}px`;
      containerRef.current.style.height = `${newHeight}px`;

      // Update state
      setSize({ width: newWidth, height: newHeight });
    }
  };

  const handleMouseUp = () => {
    setIsResizing(false);
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  };

  const toggleMaximize = () => {
    if (containerRef.current) {
      if (!isMaximized) {
        // Save current size before maximizing
        preMaximizeSize.current = { ...size };

        // Set to maximum size
        const newWidth = window.innerWidth * 0.9;
        const newHeight = window.innerHeight * 0.9;

        containerRef.current.style.width = `${newWidth}px`;
        containerRef.current.style.height = `${newHeight}px`;
        setSize({ width: newWidth, height: newHeight });
      } else {
        // Restore to previous size
        const width = preMaximizeSize.current.width || parseInt(getComputedStyle(document.documentElement).getPropertyValue('--chat-width'));
        const height = preMaximizeSize.current.height || parseInt(getComputedStyle(document.documentElement).getPropertyValue('--chat-height'));

        containerRef.current.style.width = `${width}px`;
        containerRef.current.style.height = `${height}px`;
        setSize({ width, height });
      }

      setIsMaximized(!isMaximized);
    }
  };

  return (
    <div className="chat-widget">
      {isOpen && (
        <div
          className="chat-widget-container"
          ref={containerRef}
        >
          <div className="chat-widget-header">
            <h3>Chatbot Assistant</h3>
            <div className="header-buttons">
              <button
                className="maximize-button"
                onClick={toggleMaximize}
                aria-label={isMaximized ? "Restore" : "Maximize"}
              >
                {isMaximized ? (
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <rect x="4" y="4" width="16" height="16" rx="2" ry="2"></rect>
                    <line x1="9" y1="9" x2="15" y2="9"></line>
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"></path>
                  </svg>
                )}
              </button>
              <button className="close-button" onClick={toggleChat}>
                ×
              </button>
            </div>
          </div>
          <div className="chat-widget-body">
            <ChatInterface />
          </div>
          <div
            className="resize-handle"
            onMouseDown={handleMouseDown}
          >
            <svg width="16" height="16" viewBox="0 0 16 16">
              <path d="M11 9.5L9.5 11 14 15.5 15.5 14 11 9.5z" />
              <path d="M6 4.5L4.5 6 9 10.5 10.5 9 6 4.5z" />
              <path d="M1 0L0 1 4.5 5.5 5.5 4.5 1 0z" />
            </svg>
          </div>
        </div>
      )}

      <button
        className={`chat-widget-button ${isOpen ? 'active' : ''}`}
        onClick={toggleChat}
        aria-label="Toggle chat"
      >
        <div className="chat-icon">
          {isOpen ? (
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          ) : (
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
            </svg>
          )}
        </div>
      </button>
    </div>
  );
};

export default ChatWidget;
