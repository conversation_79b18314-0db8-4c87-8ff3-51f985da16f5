.message {
  display: flex;
  margin-bottom: 1rem;
  animation: fadeIn 0.3s ease-in-out;
}

.message-content {
  max-width: 85%;
  padding: 0.75rem 1rem;
  border-radius: var(--border-radius);
  line-height: 1.5;
  font-size: 0.95rem;
}

.message.user {
  justify-content: flex-end;
}

.message.assistant {
  justify-content: flex-start;
}

.message.user .message-content {
  background-color: var(--primary-color);
  color: white;
  border-radius: var(--border-radius) var(--border-radius) 0 var(--border-radius);
}

.message.assistant .message-content {
  background-color: #f1f1f1;
  color: var(--text-color);
  border-radius: 0 var(--border-radius) var(--border-radius) var(--border-radius);
}

.message-content p {
  margin: 0 0 0.5rem 0;
}

.message-content p:last-child {
  margin-bottom: 0;
}

.message-content pre {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 0.5rem;
  border-radius: 4px;
  overflow-x: auto;
}

.message-content code {
  font-family: 'Courier New', Courier, monospace;
  font-size: 0.9em;
}

.message-sources {
  margin-top: 0.75rem;
  font-size: 0.9rem;
}

.sources-toggle {
  background: none;
  border: none;
  color: var(--secondary-color);
  cursor: pointer;
  font-size: 0.8rem;
  padding: 0;
  text-decoration: underline;
}

.sources-list {
  margin-top: 0.5rem;
  padding: 0.5rem;
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: 4px;
  font-size: 0.8rem;
}

.sources-list ul {
  margin: 0;
  padding-left: 1rem;
}

.sources-list li {
  margin-bottom: 0.25rem;
}

.more-sources {
  font-style: italic;
  color: var(--dark-gray);
  font-size: 0.75rem;
}

.source-score {
  font-size: 0.8rem;
  color: var(--dark-gray);
  margin-left: 0.5rem;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
