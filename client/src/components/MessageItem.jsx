import React, { useState } from 'react';
import ReactMarkdown from 'react-markdown';
import './MessageItem.css';

const MessageItem = ({ message }) => {
  const [showSources, setShowSources] = useState(false);

  const toggleSources = () => {
    setShowSources(!showSources);
  };

  return (
    <div className={`message ${message.role}`}>
      <div className="message-content">
        <ReactMarkdown>{message.content}</ReactMarkdown>

        {message.sources && message.sources.length > 0 && (
          <div className="message-sources">
            <button
              className="sources-toggle"
              onClick={toggleSources}
            >
              {showSources ? 'Hide sources' : 'Sources'}
            </button>

            {showSources && (
              <div className="sources-list">
                <ul>
                  {message.sources.slice(0, 3).map((source, index) => (
                    <li key={source.id}>
                      {source.metadata.fileName.split('.')[0]}
                      {source.score && <span className="source-score">({source.score.toFixed(2)})</span>}
                    </li>
                  ))}
                  {message.sources.length > 3 && (
                    <li className="more-sources">+{message.sources.length - 3} more</li>
                  )}
                </ul>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default MessageItem;
