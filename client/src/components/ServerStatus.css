.server-status {
  display: flex;
  align-items: center;
  padding: 0.5rem;
  font-size: 0.8rem;
  border-radius: var(--border-radius);
  margin-bottom: 1rem;
}

.status-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 0.5rem;
}

.server-status.checking {
  background-color: rgba(255, 193, 7, 0.1);
  color: #ff9800;
}

.server-status.checking .status-indicator {
  background-color: #ff9800;
  animation: pulse 1.5s infinite;
}

.server-status.connected {
  background-color: rgba(76, 175, 80, 0.1);
  color: var(--success-color);
}

.server-status.connected .status-indicator {
  background-color: var(--success-color);
}

.server-status.disconnected {
  background-color: rgba(244, 67, 54, 0.1);
  color: var(--error-color);
}

.server-status.disconnected .status-indicator {
  background-color: var(--error-color);
}

@keyframes pulse {
  0% {
    opacity: 0.5;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
  100% {
    opacity: 0.5;
    transform: scale(1);
  }
}
