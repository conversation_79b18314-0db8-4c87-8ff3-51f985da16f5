import React, { useState, useEffect } from 'react';
import { chatService } from '../services/api';
import './ServerStatus.css';

const ServerStatus = () => {
  const [status, setStatus] = useState('checking');
  
  useEffect(() => {
    const checkServerStatus = async () => {
      try {
        await chatService.checkHealth();
        setStatus('connected');
      } catch (error) {
        setStatus('disconnected');
      }
    };
    
    checkServerStatus();
    
    // Check server status every 30 seconds
    const interval = setInterval(checkServerStatus, 30000);
    
    return () => clearInterval(interval);
  }, []);
  
  return (
    <div className={`server-status ${status}`}>
      <div className="status-indicator"></div>
      <span className="status-text">
        {status === 'checking' && 'Checking server...'}
        {status === 'connected' && 'Server connected'}
        {status === 'disconnected' && 'Server disconnected'}
      </span>
    </div>
  );
};

export default ServerStatus;
