import axios from 'axios';

const API_URL = import.meta.env.VITE_API_URL;

// Create axios instance with base URL
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// API service functions
export const chatService = {
  // Send a message to the chat API
  sendMessage: async (message, conversationId = null) => {
    try {
      const response = await api.post('/chat', {
        message,
        conversationId,
      });
      return response.data;
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  },
  
  // Get server health status
  checkHealth: async () => {
    try {
      const response = await axios.get(`${API_URL.replace('/api', '')}/health`);
      return response.data;
    } catch (error) {
      console.error('Error checking health:', error);
      throw error;
    }
  },
};

export default api;
