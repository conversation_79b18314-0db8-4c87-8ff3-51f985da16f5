{"name": "chatbot-agent", "version": "1.0.0", "description": "A modular and configurable chatbot backend with document processing capabilities", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest"}, "keywords": ["chatbot", "llm", "vector-db", "embeddings", "semantic-search"], "author": "", "license": "MIT", "dependencies": {"@qdrant/js-client-rest": "^1.7.0", "@xenova/transformers": "^2.6.1", "axios": "^1.9.0", "base-64": "^1.0.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "langchain": "^0.0.200", "mammoth": "^1.6.0", "morgan": "^1.10.0", "node-fetch": "^3.3.2", "openai": "^4.20.0", "uuid": "^11.1.0"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.1"}}