#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const { processDocument } = require('../src/utils/documentProcessor');
const { getEmbeddingService } = require('../src/services/embedding');
const { getVectorDbService } = require('../src/services/vectordb');

// Directory containing documents to process
const DOCS_DIR = process.argv[2] || path.join(process.cwd(), 'docs');

async function processDocuments() {
  try {
    console.log(`Processing documents in ${DOCS_DIR}`);
    
    // Check if directory exists
    if (!fs.existsSync(DOCS_DIR)) {
      console.error(`Directory ${DOCS_DIR} does not exist`);
      process.exit(1);
    }
    
    // Get services
    const embeddingService = await getEmbeddingService();
    const vectorDbService = await getVectorDbService();
    
    // Get all .docx files
    const files = fs.readdirSync(DOCS_DIR)
      .filter(file => file.toLowerCase().endsWith('.docx'))
      .map(file => path.join(DOCS_DIR, file));
    
    console.log(`Found ${files.length} .docx files`);
    
    // Process each file
    for (const filePath of files) {
      console.log(`Processing ${filePath}`);
      
      // Process the document
      const { chunks, metadata } = await processDocument(filePath);
      console.log(`  Extracted ${chunks.length} chunks`);
      
      // Generate embeddings for chunks
      console.log('  Generating embeddings...');
      const embeddings = await embeddingService.generateEmbeddings(chunks);
      
      // Prepare documents for storage
      const documents = chunks.map((chunk, index) => ({
        id: uuidv4(),
        text: chunk,
        embedding: embeddings[index],
        metadata: {
          ...metadata,
          chunkIndex: index,
        },
      }));
      
      // Store documents in vector database
      console.log('  Storing in vector database...');
      await vectorDbService.storeDocuments(documents);
      
      console.log(`  Successfully processed ${filePath}`);
    }
    
    console.log('All documents processed successfully');
  } catch (error) {
    console.error('Error processing documents:', error);
    process.exit(1);
  }
}

processDocuments();
