require('dotenv').config();

const config = {
  server: {
    port: process.env.PORT || 3000,
    env: process.env.NODE_ENV || 'development',
  },
  llm: {
    provider: process.env.LLM_PROVIDER || 'openai',
    openai: {
      apiKey: process.env.OPENAI_API_KEY,
      model: process.env.OPENAI_MODEL || 'gpt-3.5-turbo',
    },
    azureOpenai: {
      clientId: process.env.OPENAI_CLIENT_ID,
      clientSecret: process.env.OPENAI_CLIENT_SECRET,
      appKey: process.env.OPENAI_APP_KEY,
      tokenUrl: process.env.OPENAI_TOKEN_URL || 'https://id.cisco.com/oauth2/default/v1/token',
      azureEndpoint: process.env.AZURE_OPENAI_ENDPOINT || 'https://chat-ai.cisco.com',
      apiVersion: process.env.OPENAI_API_VERSION || '2023-08-01-preview',
      deploymentName: process.env.AZURE_OPENAI_DEPLOYMENT || 'gpt-4o',
    },
    anthropic: {
      apiKey: process.env.ANTHROPIC_API_KEY,
      model: process.env.ANTHROPIC_MODEL || 'claude-2',
    },
    local: {
      url: process.env.LOCAL_LLM_URL || 'http://localhost:8080',
    },
  },
  embedding: {
    provider: process.env.EMBEDDING_PROVIDER || 'openai',
    openai: {
      apiKey: process.env.OPENAI_API_KEY,
      model: process.env.OPENAI_EMBEDDING_MODEL || 'text-embedding-ada-002',
    },
    huggingface: {
      model: process.env.HUGGINGFACE_EMBEDDING_MODEL || 'sentence-transformers/all-MiniLM-L6-v2',
    },
    local: {
      url: process.env.LOCAL_EMBEDDING_URL || 'http://localhost:8080',
    },
    // Azure OpenAI embedding uses the same config as the LLM service
    azureOpenai: {
      clientId: process.env.OPENAI_CLIENT_ID,
      clientSecret: process.env.OPENAI_CLIENT_SECRET,
      appKey: process.env.OPENAI_APP_KEY,
      tokenUrl: process.env.OPENAI_TOKEN_URL || 'https://id.cisco.com/oauth2/default/v1/token',
      azureEndpoint: process.env.AZURE_OPENAI_ENDPOINT || 'https://chat-ai.cisco.com',
      apiVersion: process.env.OPENAI_API_VERSION || '2023-08-01-preview',
      deploymentName: process.env.AZURE_OPENAI_DEPLOYMENT || 'gpt-4o',
    },
    // Mistral AI embedding configuration
    mistral: {
      apiKey: process.env.MISTRAL_API_KEY,
      model: process.env.MISTRAL_EMBEDDING_MODEL || 'mistral-embed',
      apiUrl: process.env.MISTRAL_API_URL || 'https://api.mistral.ai/v1/embeddings',
    },
  },
  vectorDb: {
    provider: process.env.VECTOR_DB_PROVIDER || 'qdrant',
    qdrant: {
      url: process.env.QDRANT_URL || 'http://localhost:6333',
      collection: process.env.QDRANT_COLLECTION || 'documents',
    },
    dimension: parseInt(process.env.VECTOR_DIMENSION || '1536'),
  },
  document: {
    chunkSize: parseInt(process.env.CHUNK_SIZE || '1000'),
    chunkOverlap: parseInt(process.env.CHUNK_OVERLAP || '200'),
  },
};

module.exports = config;
