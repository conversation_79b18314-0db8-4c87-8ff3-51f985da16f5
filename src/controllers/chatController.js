const { getLLMService } = require('../services/llm');
const { getEmbeddingService } = require('../services/embedding');
const { getVectorDbService } = require('../services/vectordb');
const { v4: uuidv4 } = require('uuid');

/**
 * Handle chat requests
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
async function chat(req, res, next) {
  try {
    const { message, conversationId = uuidv4() } = req.body;

    if (!message) {
      return res.status(400).json({ error: 'Message is required' });
    }

    // Get LLM service
    const llmService = getLLMService();

    let relevantDocs = [];

    // Skip embedding and vector search if SKIP_DOCUMENT_PROCESSING is true
    if (process.env.SKIP_DOCUMENT_PROCESSING !== 'true') {
      try {
        // Get embedding and vector services
        const embeddingService = await getEmbeddingService();
        const vectorDbService = await getVectorDbService();

        // Generate embedding for the query
        const queryEmbedding = await embeddingService.generateEmbeddings(message);

        // Search for relevant documents
        relevantDocs = await vectorDbService.searchSimilar(queryEmbedding, 5);
      } catch (error) {
        console.warn('Skipping document retrieval due to error:', error.message);
        // Continue without document context
      }
    } else {
      console.log('Skipping document retrieval as per configuration');
    }

    // Generate response using LLM with context (or empty context if retrieval was skipped)
    const response = await llmService.generateResponse(message, relevantDocs);

    // Return the response
    return res.status(200).json({
      response,
      conversationId,
      sources: relevantDocs.map(doc => ({
        id: doc.id,
        score: doc.score,
        metadata: doc.metadata,
      })),
    });
  } catch (error) {
    next(error);
  }
}

module.exports = {
  chat,
};
