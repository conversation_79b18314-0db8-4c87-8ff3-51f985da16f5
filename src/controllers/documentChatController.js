const { v4: uuidv4 } = require('uuid');
const { getLLMService } = require('../services/llm');
const { getDocumentSearchService } = require('../services/documentSearch');

/**
 * Document-based chat controller
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
async function documentChat(req, res, next) {
  try {
    const { message, conversationId = uuidv4() } = req.body;

    if (!message) {
      return res.status(400).json({ error: 'Message is required' });
    }

    // Get LLM service
    const llmService = await getLLMService();
    
    // Get document search service
    const documentSearchService = await getDocumentSearchService();
    
    // Search for relevant documents
    const relevantDocs = documentSearchService.search(message, 5);
    
    console.log(`Found ${relevantDocs.length} relevant documents for query: "${message}"`);
    if (relevantDocs.length > 0) {
      console.log('Top document matches:');
      relevantDocs.forEach((doc, index) => {
        console.log(`${index + 1}. ${doc.metadata.fileName} (score: ${doc.score.toFixed(2)})`);
      });
    }

    // Generate response using LLM with context
    const response = await llmService.generateResponse(message, relevantDocs);

    // Return the response with source information
    return res.status(200).json({
      response,
      conversationId,
      sources: relevantDocs.map(doc => ({
        id: doc.id,
        score: doc.score,
        metadata: doc.metadata,
      })),
    });
  } catch (error) {
    next(error);
  }
}

module.exports = {
  documentChat,
};
