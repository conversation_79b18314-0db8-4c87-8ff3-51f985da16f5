const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const { processDocument } = require('../utils/documentProcessor');
const { getEmbeddingService } = require('../services/embedding');
const { getVectorDbService } = require('../services/vectordb');

// Directory to store uploaded documents
const UPLOAD_DIR = path.join(process.cwd(), 'uploads');

// Ensure upload directory exists
if (!fs.existsSync(UPLOAD_DIR)) {
  fs.mkdirSync(UPLOAD_DIR, { recursive: true });
}

/**
 * Upload and process a document
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
async function uploadDocument(req, res, next) {
  try {
    // In a real implementation, you would use a middleware like multer to handle file uploads
    // For this example, we'll assume the file is already saved to disk
    const { filePath } = req.body;
    
    if (!filePath) {
      return res.status(400).json({ error: 'File path is required' });
    }
    
    // Process the document
    const { chunks, metadata } = await processDocument(filePath);
    
    // Get services
    const embeddingService = await getEmbeddingService();
    const vectorDbService = await getVectorDbService();
    
    // Generate embeddings for chunks
    const embeddings = await embeddingService.generateEmbeddings(chunks);
    
    // Prepare documents for storage
    const documents = chunks.map((chunk, index) => ({
      id: uuidv4(),
      text: chunk,
      embedding: embeddings[index],
      metadata: {
        ...metadata,
        chunkIndex: index,
      },
    }));
    
    // Store documents in vector database
    await vectorDbService.storeDocuments(documents);
    
    return res.status(200).json({
      message: 'Document processed successfully',
      documentCount: documents.length,
      metadata,
    });
  } catch (error) {
    next(error);
  }
}

/**
 * List all documents
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
async function listDocuments(req, res, next) {
  try {
    const { limit = 100, offset = 0 } = req.query;
    
    // Get vector database service
    const vectorDbService = await getVectorDbService();
    
    // List documents
    const documents = await vectorDbService.listDocuments(
      parseInt(limit),
      parseInt(offset)
    );
    
    return res.status(200).json({
      documents,
      count: documents.length,
    });
  } catch (error) {
    next(error);
  }
}

/**
 * Delete a document
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
async function deleteDocument(req, res, next) {
  try {
    const { id } = req.params;
    
    if (!id) {
      return res.status(400).json({ error: 'Document ID is required' });
    }
    
    // Get vector database service
    const vectorDbService = await getVectorDbService();
    
    // Delete document
    await vectorDbService.deleteDocument(id);
    
    return res.status(200).json({
      message: 'Document deleted successfully',
      id,
    });
  } catch (error) {
    next(error);
  }
}

module.exports = {
  uploadDocument,
  listDocuments,
  deleteDocument,
};
