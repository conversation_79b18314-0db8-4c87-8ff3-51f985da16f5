Error Message:

 Resource is not reachable: "BOException caught: RESULT=80004005; WHAT=GenerateStatements failed"





Cause: 

The excel file cannot be mapped to the current WEBI Report.

The excel file as source for one query and they don’t have access for the excel file.

Resolution:

If Excel file is not required, then remove the excel file query.

if you want the excel file in the report,First export the excel file to the personal folder or public folder and map to the report. 

An Excel file cannot be brought into the Query Panel. First it must be exported into Personal folders/Public Folders

Below are the steps to export the excel file to the personal folder or public folder.

Click the Folders title on the Business Objects home page. 



Open the personal folder/Public folder where you want to save the Excel file.





Click Create/Upload Objects (+)























Select Upload Document.

.                    

Click Browse, then navigate to and select the Excel file. (If needed, you can enter a new Title, a Description and Keywords. You can also assign it to a Category.)



 And then Click Add

                              







Below are the steps to map the excel file to the report.

Open the report that uses the Excel file and make sure it is in Design mode.

Click Edit  in the Query section of the toolbar to open the Query Panel.









Verify the Query tab for the Excel file is selected.













Click the three-dot Browse menu and select SAP BI Platform Repository.







Navigate to and select the Excel file in Personal folder/Public folder and click Open. 







Run the report and view the updated results. 











