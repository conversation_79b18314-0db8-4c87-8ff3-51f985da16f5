















Steps to download BO Client Tools 

Aug 2022

















































Document Information

Author(s):

BO Platform Team

Date Issued:

Aug 2022

Version Number

v 1.0



Document and Version Control 

Filename

BI4.3 SP3 Patch 6 Client Tools Installation Document

Created by

BO Platform Team

Created Date

Oct 2021

Last Updated Date

Oct 2023



NOTE: 

SAP BO Platform Admin team does not encourage the use of local installation of client tools as users may face a lot of unknown issues on the local system. We request everyone to use Client Tools via RDS (https://rds-portal.cisco.com/RDWeb/Pages/en-US/Default.aspx)









































Summary

This document describes the steps on how to download the Business Intelligence 4.3 Support Pack 3 Patch 6 (BI4.3 SP3 Patch 6) client tools. Please note that this software is for first time installations as well as for existing BO software installations.

Steps to follow:

To get access to the network path where BO Client Tools are available:

Try accessing network path \\mtv5-c01-cfs-02\wg_filer02_09\business_objects\Private\BI4\ClientTools using Method-1 or Method-2 below.

If network path is inaccessible in either of the ways, please follow the 5 steps below to acquire the access.

Step 1: Go to MY ID Groups (https://myid-groups.cisco.com/groups) 

Step 2: Select "Search Groups".

Step 3: Search for group name “SJC.BUSINESS_OBJECTSXIR” 

Step 4:  Click on Join "Action"

Step 5:  Pop-up will show join Group “SJC.BUSINESS_OBJECTSXIR” then click on Join

To go to the path from where software can be downloaded – Method 1:

Step 1: Press Win+R

Step 2: In the pop-up window type below address:

\\mtv5-c01-cfs-02\wg_filer02_09\business_objects\Private\BI4\ClientTools

 



Step 3: Press Enter or click on ‘OK’.

















To go to the path from where software can be downloaded – Method 2:

Step 1: Open ‘This PC’

Step 2: Go to Computer on top of the screen, click on Map network drive.





Step 3: In the pop-up window that appears, give the below path in “Folder” field, select “Connect using different credentials” and click Finish.

\\mtv5-c01-cfs-02\wg_filer02_09\business_objects\Private\BI4\ClientTools

 





Step 4: Username and password would be prompted. Provide them as in snapshot below.



To download the software:

Step 1: Copy ‘BI43SP3 Patch6 Client Tools’ folder from shared path to local system.





Step 2: Right click on BIPLATCLNT4303P_600-70005711application file and choose Run as Administrator. Allow it to extract completely



 



Step 3: Follow installation procedure. And click ‘ok’ if any warning screen appears









Step 4: Check if all prerequisites are succeeded, check the box and click ‘Next’.

 





Step 5: Click ‘Next’.





Step 6: Select “I accept the License Agreement” and click on ‘Next’ .



Step 7: Select checkbox and click on ‘Next’.

	



Step 8: Select Language and click on ‘Next’.



Step 9: Click on ‘Next’.





Step 10: Installation Starts.







Step 11: Click on ‘Next’





Step 12: Click on ‘Finish’.



Note: All 4.3 SAP BO client tools are 64-bit and require 64-bit DB drivers to refresh connections/reports locally.



ONLY FOR DEVELOPMENT TEAM: To enable snowflake drivers (Not mandatory for Finance applications business users)

If this is the first time you are installing BO software in your local system with BI4.3 SP2 Patch 5, it is not required to follow below steps. Please restart your PC for successful installation.

Step 1: Go to “Control Panel à Uninstall a program”. 



Step 2: Check for number of versions of “SAP BusinessObjects BI platform 4.2 Client Tools SP…” installed in your PC.

Step 3: Select the oldest version installed (first version or base version installed in your PC) and right click to select “Uninstall/change” (If you select other than base version, only “Uninstall” option will be popped-up; Make sure to select the base version to get the right option)









Step 4: Click on “Yes” when prompted after selecting “Uninstall/change”

Step 5: In the installer window that opens as below, select “Modify” and click on “Next”.





Step 6: Select Language and click on “Next”.





Step 7: In the ‘Select Features’ window, scroll down to find ‘Database Access and Security’ and select “#feature.Snowflake_DataAccess.name#” and click on “Next”.

    









Step 8: Click on “Next” in the below window to enable the modifications.





Step 9: Click on “Finish” to complete the installation.



Step 10: Reboot PC after installation completes. 

Step 11: Go to Control panel. Validate successful installation of BI4.3 SP2 P3 Client Tools.

(Optional) Steps to create Snowflake Connection:

Please follow the attached document and verify the snowflake jar file before creating the JDBC connection



Step 1: Open Information Design Tool. File à New à Relational Connection. Select any existing project or create new project before creating connection and click on “Next”.



Step 2: Provide the resource name and click on “Next”.





Step 3: Select Database: Snowflake à Snowflake à JDBC Drivers. Click on “Next”







Step 4: Provide the required details in the below window accordingly and click on “Finish” to create a Snowflake connection.



