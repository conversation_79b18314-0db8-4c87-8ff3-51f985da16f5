Oracle 11 g Client Install



Before you start the install disable your Cisco Security Agent.  

Go to Start -> All Programs -> Cisco -> Cisco Security Agent



Select Cisco Security Agent there



Select Security Settings and Set the security Level to Off.

Another way to do this is to Select Cisco Security Agent from the Bottom Right Corner 





To Install Oracle 11 g please go to the below path and double click on setup.exe

\\mtv5-vfiler02\wg-b\business_objectsxir3\Private\win32_11gR2_client\client



Select Custom  and click Next









Click Next to proceed



If the Oracle Base is not pointing to C:\oracle by default then click Browse. Select C drive and then Oracle. If you do not have Oracle folder create one. End state should look like below screen shot.





Please select all the check box except for the below unchecked.

(Do not select : Oracle Programmer, Oracle XML Development Kit, Enterprise Manager Minimal Integration, Oracle Multimedia Client Option,  Oracle Clusterware High Availability API, Oracle SQL Developer, Oracle Scheduler Agent, Oracle Services For Microsoft Transaction Server, Oracle Administration Assistant for Windows and Oracle Counters for Windows Performance Monitor) 

Click Next to proceed



















Select Ignore All check box on the right top and Click Next to proceed



Click Next to proceed





If you get Oracle Net Configuration Assistant failed error please Click Ok and ignore it.

If you get below pop up instead of Oracle Net Configuration Assistant Failure then Select Perform typical configuration and select Next



Click Skip on the bottom right side to skip the Oracle New Configuration Assistant error



Click Finish to complete installation







Once install is complete please go to 

\\mtv5-vfiler02\wg-b\business_objectsxir3\Private\win32_11gR2_client\   and copy ldap.ora and sqlnet.ora. Paste the files in the following location C:\oracle\product\11.2.0\client_1\NETWORK\ADMIN. Make sure both ldap.ora and sqlnet.ora is now available in the path where you pasted it.











