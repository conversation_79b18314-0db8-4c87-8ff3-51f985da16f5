Summary

This document provides list of standard requests/issues solution documents in SAP Business Objects platform that user can self-help to fix/fulfill requests.

Solution Documents

S. No.

Topic Name

Solution Document

Comments

1



Snowflake JDBC Setup for SAP BO

 Snowflake JDBC Setup

This is for snowflake client drivers' installation in local PC.

2

Install/Upgrade BO Client Tools in local PC

Install/Upgrade BO Client Tools in local PC

This is for BO Client Tools Installation in local PC. Note: Platform team does not encourage the use of local client tools. We request users to use client tools via citrix (Point 6).

3

BO Report Related Queries (Database Error, Authorization Error, Data Not Retrieved, etc,.)

BO Report Related Queries (Database Error, Authorization Error, Data Not Retrieved, etc,.)

Please go through the attached excel for the universe used in the BO report and raise case with the respective Service Offering/Assignment Group. 

 4

Audit usage Report for different BO applications on monthly basis.

Audit Usage Report Details

Scheduled Audit usage report with different BO applications on monthly basis. Please contact respective BO application support teams.

5

Access to BO Tools via Citrix

Citrix Access Process

To get access to BO Tools (Designer, IDT, Rich Client) via Citrix, please follow the steps in the attached document.

6

BO Report Common Errors

BO Report Common Errors

SFTP Destination Fingerprints

This document gives resolutions to some of the issues and errors encountered while scheduling the reports.

SMTP Error

Destination Error

Binary output error

Bad Connect String Error

BOException Error

Schedule to SFTP:

Please use the latest fingerprints from attached documents for your respective SFTP destinations. 











7















Reverse Migration (production to Stage/Dev)





Reverse Migration





To migrate the Universes and reports from Production to non-prod environments please follow the process attached in the document







8

Login Issue

Login Issue



To get the BO application access or login issues please refer the attached document

9

Steps to schedule the reports in SAP BO



Steps to schedule reports in SAP BO

For scheduling BO reports please refer to the attached document.



10

How to save report in BO Prod

How to save Report in BO

Could not save the document to the repository when tried to save report in public folder. Please save the report in personal folder. Please reach out to application team who can help to update the report in public folder with CR

11

Exception or PMT access

Exception or PMT access

To get access to retrieve a greater number of rows in a BO report or to get access to be able to promote content from dev to stage.

12

How to send Report to BI Inbox

How to send a report to BI Inbox.docx

To send a report to other users BI Inbox.





SAP BO Platform Questionnaire scenarios 

Following information is required for looking into below issues.

1)Application access:

Application/Universe name 

If you are not sure about Application/ Universe details, please provide CEC Id of team member who is having the similar access.

2)Data related requests:

For Data related issues if you are aware of Application/Universe Details please raise a case w/r to that application by using the following link.

Service Portal - Old HelpZone (service-now.com) (Search for required Application/Universe name in search help zone) 

If not aware of Application/ Universe name, please send that report to BI inbox. (dbejjank, prpala, tapurush, srkothap, aasbhatn)

3)Connection Password Update

Connection name 

Database Username

Universe/Application

Platform (Enterprise, Dise, Finance) & Environment (Dev, Stage, Production) Details 

4) Reverse Migration Requests:

Universe/Application name 

Report path

Source and Destination Environments

5) PMT Access Request:

PMT access provides access to the Promotion Management Tool which is used for migrating content from Dev to Stage. Migration from or to production is solely taken care by the BO platform team via an Incident for prod to non-prod and CR for non-prod to prod. 

This access can only be provided by the BO platform admin team after reviewing the business justification provided. 

A maximum of 2 users can be added to the PMT group to track changes made effectively. If users request adds one more user, we will update user as below.

Need to remove the existing user access and will provide access to new user. 

6) SAP BI Launchpad URL Issues:

Did you try in browser cache/Try in Incognito mode/Private window.

Did You Check Network/VPN connected.

Make sure using proper URLs and providing Correct Credentials.

Please find the URLs list below.

Login Issue

7)Exception Access:

 

Exception access refers to the ability to retrieve a greater number of rows in a BO Report. By default, users can retrieve 500K rows from a BO report. Some users might require fetching more data for which they can request exception access i.e., 2M (2 million records) and 5M (5 million records) exception group access. 

This access can only be provided by the BO platform admin team after reviewing the business justification provided and need vavunuri and pallered Approvals. 

As a Standard we will provide Exception access for limited people, if still application teams request for more users to access, we will update them as need to remove the existing user access to provide access to new user.

 

8)SFTP Fingerprint error:

Please find the link consists of the Updated SFTP Fingerprints with Sftp host names

                                     SFTP Destination Fingerprints

If still facing issues, please raise a case to BO platform through Following link.

                      Service Catalog - HelpZone (service-now.com) 

9)CR Deployment: 

Every Tuesday we have a CR deployment review call at 11 AM IST and 11 PM IST where we will review the Changes that are made in stage environment. The meeting invite is sent one day prior to the CR review call by BO platform team to all the CR owners.

Before the CR Review call, all CRs must be approved by respective Application team managers. Post review, the CRs will be approved by Platform Team (D&A BO Platform Operations).

Please ensure to attach the CR review document to the CR before the review call – containing the description about the changes made, Webi run screenshot, Integrity check screenshot and all other relevant information.

As per our standards deployments are implemented only on Thursdays. If there is any Database /ERMO dependency, we will go with other days with a proper justification.

If Deployment needs to be performed during ME/QE freeze period, then Director level approval is required along with D&A BO Platform Operations.

 

 

      

 

 

 

 

 

 

 

 

 

 

 

          

















