

SAP BI 4.3 Common Issues, FAQ & Tips



Contents

Document information:	2

Document Version Control:	2

Who is this document for?	3

Document Relevance	3

Communication Timelines:	3

BO Upgrade Timelines:	3

First things first:	4

SAP’s User Demo Session Videos:	5

FAQ: What’s New in Nutshell:	5

FAQ: Cannot login to BI Launchpad	6

FAQ: How to install SAP BO Client tools on your laptop:	7

FAQ: Cannot login Using Webi Rich Client Installed on Laptop:	7

FAQ: How to Use client tools from Citrix:	10

FAQ: Cannot access Client tools from Citrix:	11

FAQ: Slow / Performance issues	12

1.	Try the Incognito/ Private window mode:	13

2.	Changing browser:	14

3.	Clearing Browser caches:	14

FAQ: When changing the query in the query panel after run changes are not reflecting	15

FAQ: Search for universe objects:	17

FAQ: Send to: User name search doesn’t show up results:	18

1.	Search return No data immediately after clicked on search:	19

2.	Search returns multiple values	20

FAQ: Working with Excel file as a data source to your Webi Documents:	21

1.	What it was earlier?	21

2.	How has it changed in latest version?	22

3.	What can we do to fix it?	23

4.	What workarounds are available?	24

0.	Know Excel file being used by your Webi intelligence document:	24

1.	All Dimensions:	27

2.	Mixed Qualifications:	28

3.	Upload Excel file to BO	28

FAQ: Edit Home page Tiles	31

FAQ: How to work with multiple Webi documents simultaneously?	32

Tips and Tricks for Webi Usage:	33

Webi 1: So many changes! It does not look like earlier.	33

What did not change?	33

What has been changed?	33

Panel overview:	34

Webi 2: Schedule directly from home page	34

Webi 3: Quick Switch	34

Webi 4: Make use of Favorites	35

Webi 5: Check out the Auto refresh button	36

Webi Z: Gen Z’s way to use Advanced Webi	37











Document information:

Author(s): 

BO Platform Team 

Date Issued: 

Aug 2022 

Version Number 

v 1.0 



Document Version Control:

Filename 

SAP BI 4.3 Common Issues and FAQ

Created by 

BO Platform Team 

Created Date 

Aug 2022 

Last Updated Date 

Aug 2022 





Who is this document for?

Anyone who intends to use / work with New BI 4.3 applications. 

Document Relevance

This document is expected to encompass all the frequently asked questions by anyone who is trying to use SAP BI 4.3 tools.



Communication Timelines:

Communication was directed to respective teams well in advance. Please refer to following:

BI 4.3 Upgrade Communication Timelines:

June 16th - Webex Space created inviting all the application teams for BI 4.3 Testing in POC 

July 14th - BI 4.3 Upgrade timelines were published (First Communication)

July 1st - User Demo session meeting invites were sent 

July 13th - User Demo session reminder was sent 

Aug 1st - BI 4.3 Upgrade timelines were published (Second Communication)



BO Upgrade Timelines:







Upgrade Timelines:

First things first:

Please read the links we had shared in the BI 4.3 enablement GC 

Here are quick links:

What's New in 4.3:

https://blogs.sap.com/2020/06/30/sap-businessobjects-bi-4.3-whats-new/

https://blogs.sap.com/2020/06/15/sap-bi-4.3-whats-new-in-web-intelligence-and-semantic-layer/

https://blogs.sap.com/2020/12/01/sap-bi-4.3-sp1-whats-new-in-web-intelligence-and-semantic-layer/

https://blogs.sap.com/2021/05/30/sap-businessobjects-bi-platform-4.3-sp01-available-on-sap-cloud-appliance-library/

In case you it is convenient to refer to pdfs; please save them to your desktop:





SAP’s User Demo Session Videos:



SAP had conducted User demo sessions as mentioned in above timelines. Please refer to the recording links for the same.

July 17th: https://cisco.webex.com/cisco/ldr.php?RCID=624f06f4fff19aab93aba1558e673c27 

Password: EupzF37v 

 Aug 4th: https://cisco.webex.com/cisco/ldr.php?RCID=4fe5018dfcb75d7fab621f00ab3f8976 

Password: EpxdPny8



FAQ: What’s New in Nutshell:



In case you do not have scope to go through the above links/ documents or videos: 

You may note just the following:

No need to change your links / bookmarks. All Links remain the same; Enterprise: https://bi4cisco.cisco.com/BOE/BI

Finance: https://bi4fin.cisco.com/BOE/BI

Your user profile, your access/ authorizations remain the same. No change in security.

No functionality is removed. BO 4.2 and 4.3 have full feature support. 

Look and feel is changed vastly. Earlier BI Launchpad used to depend on Java whereas now it is HTML5 across. Therefore, it will be faster and smoother for normal browsing.

No feature difference in Webi Rich client Desktop installation vs the Web application. Viz. the Features available when using Webi Rich Client installed on your laptop will be same as the one using above BI Launchpad links. That means, there should be no need to install Webi Rich client separately to access SAP BO reports/ queries.

If you still need to install SAP BO client tools, please refer the below guides. It is recommended to use Client tools from Citrix. 



FAQ: Cannot login to BI Launchpad 



Error: Account information not recognized..

Please check the User Name field

Incorrect:



Correct:



Please note the above screenshot: 

In the Username field please enter your CEC id. And not the email address. 

The Password is your normal CEC / windows login password. 

SAP BO supports only login using CEC/ password viz. LDAP account. 



FAQ: How to install SAP BO Client tools on your laptop:

Please follow our document: Install-Upgrade BO Client Tools to 4.3 in local PC.docx

You might also need: TD 64 bit Drivers Installation Steps.docx



FAQ: Cannot login Using Webi Rich Client Installed on Laptop:



Once you have installed Webi Rich client on Your laptop and launch it, you might see following issue.





Here, the above Authentication Mode drop down has vanished. To be able to login, the authentication mode must be set to LDAP. 

Solution/ Workaround: This is a known issue which persist since years. It appears because Cisco Internal network/ Firewall blocking connections from SAP BO Client tools. We have observed this issue for some location using specific VPN. Please close the client tools and try RTP/ San Jose DUO SSL as the issue is least observed with the users using these VPNs. 



Check current VPN:





Change VPN:





The Authentication modes should appear once above steps are followed:









FAQ: How to Use client tools from Citrix:



If you are existing user, you just have to login to Citrix, and we have made available new tools. 

Citrix Link: https://citrix-gis.cisco.com/Citrix/CITRIX-GISWeb/

If you are new user, please refer to the document below. 

Access to BO Tools via Citrix.docx



FAQ: Cannot access Client tools from Citrix:



Issue Nature: Faced Intermittently by few users

Once you have access to Client tools on Citrix you should see the below page:





Upon opening ica file:

You might get stuck with following:



Status: SAP BO Platform team is working with Citrix team to fix this issue. ETA: NA

Workaround 1: Click on cancel, log out from Citrix, close browser, open browser again, Login to Citrix and open the app again.

Workaround 2 (if 1 doesn’t work): Please try to change VPN to RTP/ San Jose DUO SSL as the issue is least observed with the users using these VPNs.

For any information or any other issues while accessing Client tools via Citrix, please contact:

Citrix team: <EMAIL>

You may copy us too: <EMAIL>



FAQ: Slow / Performance issues



For the First time, if you access https://bi4cisco.cisco.com/BOE/BI

You might encounter, a blank page!





Solution: Patience, please allow least 20-30 seconds for the page to load for the first time.



If it doesn’t work for you please follow... 



Try the Incognito/ Private window mode:





Once Browser windows loads, please use the same BI Launchpad link.



Changing browser:



Try using Chrome/ Edge or Firefox. Accessing the same URL.

Ensure that you are connection to VPN to be able to access the same.





















Clearing Browser caches:



If the link works after using Incognito window / Private window; please follow below steps to clear your browser caches.  

Browser

How to clear browser cache?

Shortcut

Microsoft Edge

Click Hamburger icon in the top right> Select History> Click on Meatballs Menu (Three horizontal Dots)> Clear Browsing Data> Time range: All Time> Clear now.

CTRL + SHIFT + DEL

Google Chrome

Click on Kabab Menu (Vertical three dots)> Click History> Clear Browsing Data> Time range: All time> Clear Data

CTRL + SHIFT + DEL

Mozilla Firefox

Click Hamburger icon in the top right> Select History> Clear Recent history> Select All the time> Ensure all checkboxes are ticked. 

CTRL + SHIFT + DEL

Internet Explorer

It is not recommended to use this browser for SAP BO apps. BO 4.3 apps might never work with this browser.





For screenshots, please refer to this link.

FAQ: When changing the query in the query panel after run changes are not reflecting



Once you updated the query panel, please Apply change and close then in report pane you can run the report .

After running the report you can do Save or Save As so that next time when you open the report the changes will be updated 

Apply change and close option in Query pane



Run Report pane:



Save:



FAQ: Search for universe objects:



In the older version of Business Objects (BI 4.2 SP9P3), we were used to instant search or search while typing searching Universe objects. I.e 





In current production (BI 4.3 SP2 P5), this behavior is slightly changed. Once you type the search item, you will have to click on search or hit enter so that BO can search. It will not search automatically as you type.

Before clicked on search:



After Clicked on Search:





FAQ: Send to: User name search doesn’t show up results:



The Send to functionality has not been changed. However, the look and feel of the send to prompt has changed. To search with CEC id, you must select Find title and enter the CEC id, click search.







On this screen following scenarios have been observed:

Search return No data immediately after clicked on search:







Solution/ Workaround: Patience. This is known issue, there is a lag while displaying results after clicking on Search. In above case it has been observed that the search returns results after 10 to 20s provided we remain on the same screen.  We are working with SAP (vendor) to find if there is any solution to this issue.

After 10-20s:



Search returns multiple values

It has been observed that the search returns multiple values of same CEC ID.





Solution/ Workaround:  This a result of multiple clicks on search button while the page displayed No data. After searching once, if you notice that the page displayed No data (above screenshot in Scenario 1) and on this screen if you click Search button multiple times, it will result into multiple search queries to be triggered. These multiple queries asynchronously return same result in the below page; hence you see multiple results of same object. In this case, the functionality still works, and you can choose any of the above results to send your document. Note: The search results will be repeated as much as many times you have click on search while it displays No data.



FAQ: Working with Excel file as a data source to your Webi Documents:



It has been observed that there is behavior change when working with excel files as data source for Web Intelligence documents.



What it was earlier?

When you create a new Webi report based on Excel data Provider, you would see following screen before importing the data set. 



This Object Properties pane allows us to change object qualification even before the data provider was loaded or refreshed. 



How has it changed in latest version?

The above Objects Properties pane is omitted out in newer version

So, objects from excel automatically take Qualification to Measures and Dimensions.

Create webi document Using excel as data source:





Note: BO automatically qualifies objects into Measures and dimensions.

What can we do to fix it?

Nothing, BI Platform team is working with SAP (vendor) to have the object properties pane back to where it was. However, this might take months. 



What workarounds are available?

There are couple of work arounds available depending upon your scenario / use case. 

Note: The Number format needs to be changed in the excel file before adding the excel as data provider to the report. 

Know Excel file being used by your Webi intelligence document:

Find the Webi repot based on Excel data source. Click on modify.





Click on edit query:





Locate the query with Excel report> Click on Edit Settings:





Note the File name, which contains the Full path of the Excel Document.





Locate the Excel File.

If the above path starts with / then the path refers to Public folders from Business Objects.





For this demo, the Excel file is in Web Intelligence Samples folder

Select the excel file, right click, and choose View. This will allow you to download the file to your laptop/ desktop.



View will download the file to your laptop. 

Open the above excel file and check the Number Format.

 





All Dimensions:

You need BO to qualify all Objects in Excel to be Dimensions then use Number format as Text in the excel file.





Once you save the excel file, you will have to re upload the same. This is explained in below.



Mixed Qualifications:

If you want BO to qualify few Columns as Dimensions whereas others as Measures, then in the excel choose those columns as Number. 





Upload Excel file to BO

Once you have saved the Excel file with desired Number format according to your requirement above, you will have to upload the same to BO. 

	You may add local document the way you did earlier:





This will add the above chosen Excel file to Business Objects.



You may also replace a existing Excel File. Right click the Excel file> Choose Organize > Select Replace File.





Browse and select the File that you have saved in Step 4 and click Replace.







Confirm to Replace. A text message should appear that the File is uploaded successfully. 

In case you are editing existing Excel file, you can also choose to replace the one. However, it is important to know that once you replace the file it will not break existing reports or update them automatically.  The changes from excel file will only reflect once you create a new report based on same excel. 



FAQ: Edit Home page Tiles



Following is the default view:





To change the tiles here, please click on your profile>  Settings> Account Preferences>

Turn off Use Administrator Provided Settings> Save> Log off and Login again> You should be able to Change tiles now.

 





FAQ: How to work with multiple Webi documents simultaneously? 



Previous SAP BO Version allowed to open multiple Web intelligence documents and access them in tabbed manner. In-page Tabbed browsing is no longer available SAP’s new Fiori Launchpad. 

However, here similar functionality can be achieved using drop down. 

Login to BI Launchpad> Open Any report> Click Home> Open Another Report> Click Home and Open Another one. 

Click on Drop drown here to navigate to earlier opened reports. You may also close them using shortcut here. 



Note:

You may Navigate between reports even when they are being edited (modified) using Design Mode.

You may Navigate between reports even while they are being refreshed. 

You cannot navigate while you are completing an action using modal prompt. Model prompts are your query panel/ prompt screen/ parameter/ LOV screen etc. To be able to navigate between reports, you should first close the Modal prompt viz. while editing the query please click Apply and Save post which you can refresh instead of clicking Run button. This way, you can keep the existing report to refresh and navigate away. Please refer to screenshot below.









Tips and Tricks for Webi Usage:



Assuming you have basic now how and information about Web Intelligence tools and services. 



Webi 1: So many changes! It does not look like earlier.



Context: You upgrade your phone every few years; New models show up ever few months! The look and feel changes drastically even you are loyal to the brand / carrier. In 2022, we cannot have age old user interface which is not faster/ secure or up to the required Web standards. Similar analogy goes with Analytical applications too. SAP has been keen to introduce new things as well as revamp the UI up to new standards. 

What did not change? 

URL for BI Launchpad / Respective system names used for Web Intelligence client tools.

Query panel – except few icons and LOV screen

Fundamentals on how webi works including your context calculations/ variables etc.

Right-click options/ naming conventions etc. 

What has been changed?

Unified Query panel (looks same for all type of data sources)

New data sources, new objects/ panel icons/ refresh button location, LOV screen/ Navigation.

Re-grouping top ribbon icons

Everything has properties!



Panel overview:

Main Panel

Filter Bar

Side panel

Toolbar



Document summary

Navigation Map

Report Filter and Structure

Available Objects 

Shared Element

Comments

Input controls

User Prompts (View mode)

Data Assignments

Turn into Report elements

Analysis Tab

Format Chart / Tables

Object Property

Refresh Data etc



Webi 2: Schedule directly from home page 

After you login; you see Recent Documents and Recently Run. 

You can choose Meatballs menu and schedule/ get right click context menus while on homepage.



This will save some of your time navigating to the document and then use the same.



Webi 3: Quick Switch

Cycle between open documents:





For more information: Please check above FAQ: How to work with multiple Webi document simultaneously?



Webi 4: Make use of Favorites 



Mark documents that you use daily as favorite and view them on one screen regardless of which folder they belong to.







Pro tip: You can mark a folder as favorite too!



Webi 5: Check out the Auto refresh button

Once you are on Schedule Instances page and waiting for your schedules to complete. You can use Auto refresh button and avoid clicking on refresh page.









Webi Z: Gen Z’s way to use Advanced Webi

Who is it for? If you call yourself:  I am a season developer/ I am extraordinary noob/ I know my stuff/ I want see Advanced Webi.

Leverage these features:

Webi Data model

New buttons (Ellipses / setting: Instant apply)

Presentation modes/ Theming

If you are amongst the ones who likes challenge and would like to dive deep, here is a link to the holy grail of Webi Tips and Tricks. Explore it in your own time. 

