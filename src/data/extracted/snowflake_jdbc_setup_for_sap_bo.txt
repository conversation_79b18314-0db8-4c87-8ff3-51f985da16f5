

Snowflake JDBC Setup for SAP Business Objects Client



Contents

Downloading and configuring Snowflake JDBC Drivers	2































Author

Version

Last Updated

Vijay Avunuri

1.0

03/25/2019

BO Platform Support Team

1.1

11/02/2020

BO Platform Support Team

1.2

09/02/2022





Downloading and configuring Snowflake JDBC Drivers

Download Snowflake Client Drivers 

Install/Upgrade the BO Client Tools in your PC to Business Intelligence 4.3 Support Pack 9 Patch 5 (BI4.3 SP2 Patch 5) version for enabling the Snowflake Connectors using the steps in the following document.



Download snowflake jdbc drivers from the below link:

https://repo1.maven.org/maven2/net/snowflake/snowflake-jdbc/3.12.4/snowflake-jdbc-3.12.4.jar 

 

Configuration steps on local desktop 

On the local desktop, go to C:\Program Files (x86)\SAP BusinessObjects\SAP BusinessObjects Enterprise XI 4.0\dataAccess\connectionServer\jdbc\drivers and create a new folder “snowflake”.

Copy the “snowflake-jdbc-3.12.4.jar” to the above snowflake folder

Connection must be created/updated as in the below snapshot format.



