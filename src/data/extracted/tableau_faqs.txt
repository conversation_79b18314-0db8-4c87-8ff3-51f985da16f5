1 |  Recently Asked Questions

Renewing Tableau Desktop License​​​​​​​

If your Tableau Desktop license is expiring soon and if you obtained the tableau desktop license from CDAO team then no action is needed from end user, as part of the auto renewal process your tableau desktop license will automatically renew prior to the license expiration date.The procurement team is working on extending it further.​​​​​​

2 |  Tableau Server FAQs

Q1  We need to evaluate Tableau Server. What are the options?​​​​​​​Q2  We are done with the evaluation, We need a production Tableau account. What do I do?​​​​​​​Q3  I need access to an existing Tableau Site. Whom should I reach out to?​​​​​​​​​​​​​​

Q4  How to change your Tableau Server Site Name?​​​​​​​Q5  Unable to Log in to the Development and Stage Servers.

Q6  How to access the Sandbox Enterprise Tableau Server?Q7  Need access to https://centro.cisco.comQ8  Need Tableau Prep License​​​​​​​

Scroll to top

Q1 | We need to evaluate Tableau Server. What are the options?

​You can request for a Sandbox Account. It is a temporary Tableau account which is active for 90 days. It is limited to 15 users only.

Create a sandbox/POC account here: eDnA SiteGo to Data Visualization -> Tableau -> Create Sandbox Account​​​​​​​

Scroll to top​​​​​​​Scroll to Tableau Server FAQs



Q2 | We are done with the evaluation, We need a production Tableau account. What do I do?

​You can raise a request for production account from eDnA.Please follow the steps mentioned below to create a new site on Enterprise Tableau Server:

How to Set Up Dev/Stg Site

Go to edag.cisco.com and sign in (cec)

Go to the Data Platforms > Tableau > Project On-boarding Account

Fill out all the details and hit submit.

Once submitted, it will go for approval to your manager and the Tableau Platform team

Once approved by both, the site will be created in Tableau Dev (https://dev-tableau.cisco.com) and Stage (https://stg-tableau.cisco.com) Environments 

Promoting site to Production:

Once the site has been created in Dev and Stage environment, you need to promote the site to production.

Go to edna.cisco.com and sign in (cec)

Go to Requests & Approvals > Requests > Applications & Accounts

Select your application, Go to the Stage Tab & then hit "Promote to Prod". Refer to sample image below.

Once promoted to prod, it will raise another approval from Manager and the Tech team.

Once approved by both, the site will be created in Tableau Prod Environment (https://tableau.cisco.com). 

Note: We are working with the eDNA team to unify both the process so only single approval will be needed and the site will be created in all the 3 environments.

Scroll to top​​​​​​​Scroll to Tableau Server FAQs



Q3 | I need access to an existing Tableau Site. Whom should I reach out to?

To obtain access to an existing tableau site, please open a ticket to enable  Viewer/Explorer  access, ensuring that the ticket includes an approval comment from one of the site administrators. and user manager 

 

And also, include the direct URL of the Tableau dashboard that your trying to access in the ticket notes​​​​​​​

Scroll to top​​​​​​​Scroll to Tableau Server FAQs



Q4 | How to change your Tableau Server Site Name?

Please open a Ticket with Tableau Admin Team including the Server and site URL along with New Site Name (This process will be Automation through Edag)



Q5 | Unable to Log in to the Development and Stage Servers.

Our development and stage Tableau servers are now using the non-production SSO servers. We’ve found that the non-prod passwords of a significant percentage of users are not in sync with their production CEC passwords. A user who has been provisioned on either development or stage may still get a single-sign on error as shown below.



If so, please follow the instructions below and everything should start working.

*THE USER MUST PERFORM THIS ON HIS/HER OWN*

Sync user from PROD to STAGE​​​​​​​

​​​​​​​​​​​​​​Copy this URL into the address bar of a browser tab: https://cdca.cloudapps.cisco.com/cdca/synch.do​​​​​​​

We can't create a standard link as the page breaks when accessed through a link(you MUST copy and paste)

Enter your CEC user ID.

Sync From DB “CPRPROD”

Sync To DB “CPRSTG”

Enter your CEC password.

Please wait 10-15 minutes to let the changes propagate to the appropriate servers. 

Original Author: Ben Torgesen [btorgese]

Attachment: View Folder

Scroll to top​​​​​​​Scroll to Tableau Server FAQs



Q6 | How to access the Sandbox Enterprise Tableau Server?

Using the SANDBOX Enterprise Tableau Server

A Tableau site for your team has now been created on our sandbox (proof-of-concept) server.  If you are reading this, you are likely one of the site administrators.  This means that you have full control of the content, users, security, and processes within your site. You can add additional site admins as needed.  As a site admin, you can add/remove users along with creating new projects (for grouping workbooks) and new groups (for grouping users).  You can also publish workbooks to the servers.  However, you don’t have to be a site admin to publish workbooks.Sandbox (POC): http://poc-tableau.cisco.com/ (15 user limit)

The sandbox Tableau server is for temporary testing of Tableau server functionality. Your site will automatically be deleted after 90 days.

Please make sure you are using the appropriate version of Tableau Desktop. There can be incompatibilities between Tableau Desktop and the version of Tableau Server installed at Cisco. You can always find the appropriate link to download Tableau Desktop on our Architecture page (https://cisco.jiveon.com/groups/cisco-tableau/pages/tableau-architecture).

We highly recommend you watch some of the On-Demand Training videos on the Tableau web site. There are also some videos detailing administration tasks which will be useful (see below).

Adding Users & Groups:http://www.tableau.com/learn/tutorials/on-demand/system-administration 

Projects, Groups & Permissions:http://www.tableau.com/learn/tutorials/on-demand/tableau-server-content-administration 

Shared Data Sources:http://www.tableau.com/learn/tutorials/on-demand/data-server 

Authorization & Permissions:http://www.tableau.com/learn/tutorials/on-demand/authorizations-and-permissions

Please join our Jive group, Cisco Tableau. You will find important announcements on the Overview page and the Tableau Center-of-Excellence page will contain best practice documentation as well as additional training links.

Also, there are two aliases in which you might be interested.  Please subscribe to the “enterprise-tableau-users” mailer alias.  We use this alias for sending out notifications about server issues, upgrades and scheduled outages.  An optional alias is "enterprise-tableau-coe" which is for our Center-of-Excellence.  Tableau users can ask "how do I" questions of other members of the alias. In addition, you can join our monthly Center-of-Excellence meeting by downloading the meeting invite (TableauCOE.ics) here.​​​​​​​Welcome to the Tableau community at Cisco!​​​​​​​

Original Author: Ben Torgesen [btorgese]​​​​​​​​​​​​​

Scroll to top​​​​​​​Scroll to Tableau Server FAQs



Q7 | Need access to https://centro.cisco.com

​Please drop an email to <EMAIL> for getting access.​​​​​​​

Scroll to top​​​​​​​Scroll to Tableau Server FAQs



Q8 | Need Tableau Prep License

​Please follow the below steps to activate Tableau Prep License.

 Open Tableau Prep Application

Go to Help > Click Manage Product Keys



Click on Activate



 

Click on Activate by Signing in to server



Provide https://cr-analytics.cisco.com/#/site/On-Prem-Creator-License/home and click connect



Provide your cisco login credentials.

 

 Once your authentication is successful, your tableau prep license is activated.



 

Scroll to top​​​​​​​Scroll to Tableau Server FAQs

3 |  Tableau Desktop FAQs

Q1  Is there a demo license for Tableau Desktop as well?Q2  How do I request for a Tableau Desktop Creator license ?​​​​​​​Q3  How Deactivate Tableau Desktop Creator license?

Q4  Upgrading Tableau DesktopQ5  How to Downgrade the Version of a Workbook?​​​​​​​​​​​​​​Q6  Connection timeout error while connecting to Tableau server.

Q7  What version of Tableau Desktop should I download?​​​​​​​Q8  How do I connect snowflake from tableau?​​​​​​​​​​​​​​​​​​​​​

Scroll to top

Q1 | Is there a demo license for Tableau Desktop as well?

Tableau Desktop comes with a 14-day evaluation which can be used for Demo purpose​​​​​​​

Scroll to top​​​​​​​Scroll to Tableau Desktop FAQs



Q2 | How do I request for a Tableau Desktop Creator License?

Please follow the below link to request Tableau Desktop Creator License 

Scroll to top​​​​​​​Scroll to Tableau Desktop FAQs



Q3 | Enterprise Tableau Deactivating Tableau Desktop Creator License

​Please follow the steps mentioned below:​​​​​​​​​​​​​​​​​​​​​

Open Tableau Desktop.

Click on the menu item Help -> Manage Product Keys.

Click on the Product Key "Creator" and the select the “Deactivate” button​​​​​​​

Original Author: Ben Torgesen [btorgese]​​​​​​​

Attachments: View Folder​​​​​​​

Scroll to top​​​​​​​Scroll to Tableau Desktop FAQs



Q4 | Upgrading Tableau Desktop

​Tableau upgrades can be broken down into two types of upgrades, minor releases and major releases.

Minor Releases

Come out almost monthly.

Typically include bug fixes and some new functionality.

Tableau file formats remain the same.

They are designated by a numeric change in the third position of the version number (e.g. 2023.3.1 to 2023.3.8 etc...)

Tableau Desktop and Tableau Server are compatible between different minor release versions (i.e. Tableau Desktop 2023.3.3 could publish to Tableau Server 2023.3.1). 

Major Releases

Usually happen about once a year

Include major changes to the architecture or large new pieces of functionality

They are designated by a numeric change in the first or second position of the version number (e.g. 2023.3, 2024.1, 2024.2 etc...).

Typically include a change in the Tableau file formats.  These file format changes mean that Tableau Desktop and Tableau Server are not compatible between the different versions.  If a user upgrades Tableau Desktop to a major new release, they won't be able to publish any of their workbooks until the Tableau Server has been upgraded also. 

We recommend that business users wait to upgrade their Desktop software (for major releases) until all three environments (DEV/STG/PRD) of the enterprise Tableau servers have been upgraded. However, sometimes users may have a need to test the newer version prior to the server upgrade.  This does work as long as the user either has no need to publish workbooks during the time the servers are incompatible -OR- the user installs the new version in addition to keeping the older version on their computer.   If you install two different major version of Tableau Desktop on the same computer for testing purposes, you should take the following precautions…

Change the repository location (File -> Repository Location…) for the new version of Desktop to a different folder.

Files that have been saved using newer version Tableau Desktop CANNOT be opened again in the older version of Tableau Desktop and CANNOT be published to older version of Tableau Server.

Whenever you open an existing workbook using the new version of Tableau Desktop, immediately save it using “File -> Save As…” and choose the new repository location.  This will keep your original files from being overwritten and allow you to continue publishing those files to the Tableau Server using the older version of Tableau Desktop. 

The current version of the enterprise Tableau Server environments as well as links to the currently recommended Tableau Desktop versions to download can be found on our Architecture page (https://cisco.jiveon.com/groups/cisco-tableau/pages/tableau-architecture​​​​​​​(Delete after reading - Is this the correct Link?).You can also download and install any version of Tableau Desktop from  below link 

https://www.tableau.com/support/releases/desktop/2023.3.1

Please see this Tableau Knowledge Base article for more information about upgrading Tableau Desktop: http://kb.tableausoftware.com/articles/knowledgebase/upgrading-tableau-desktop.

​​​​​​​Original Author: Ben Torgesen [btorgese]

Scroll to top​​​​​​​Scroll to Tableau Desktop FAQs



Q5 | How to Downgrade the Version of a Workbook?

Using Tableau Server or Tableau Cloud

1. Download as Older Version:

Open the workbook in Tableau Server or Tableau Cloud. 

Select Download > Tableau Workbook from the toolbar. 

During download, specify the version you want to downgrade to 

For more information please refer this link

Scroll to top​​​​​​​Scroll to Tableau Desktop FAQs



Q6 | Connection timeout error while connecting to Tableau server.

​​Uninstall the Tableau desktop and re install it.

 

Scroll to top​​​​​​​Scroll to Tableau Desktop FAQs



Q7 | What version of Tableau Desktop should I download?

​Please download the 2023.3.1 version of Tableau Desktop to avoid any incompatibility issues.​​​​​​​Link: https://www.tableau.com/support/releases/desktop/2023.3.1

Note: If you encounter any issues accessing this link, consider creating an account.

Scroll to top​​​​​​​Scroll to Tableau Desktop FAQs

4 |  General Queries

Q1  I am new to Tableau, where can I learn more about Tableau?​​​​​​​Q2  What are the business teams responsibility?​​​​​​​

Q3  Does the platform team support custom development on Tableau?​​​​​​​Q4  What are the Cisco Color Palette's for use in Tableau?​​​​​​​

Q5  What are the Recommended Browsers?​​​​​​​

Q6  Date format not working properly while downloading the data from tableau server dashboard

Scroll to top

Q1 | I am new to Tableau, Where can I learn more about Tableau?

​There are many learning modules available on the internet. One of them being: https://www.tableau.com/learn​​​​​​

Scroll to top​​​​​​​Scroll to General Queries



Q2 | What are the business teams responsibility?

Business Teams Responsibility

Learn Tableau (taking advantage of identified training materials, but also searching Tableau forums and searching the web for answers to problems)

Manage your own data (have access to either Excel/CSV/text files or database login credentials, understand how to join and filter your data, create calculated columns, etc...)

Build your own dashboards (create individual visualizations, combine multiple visualizations on a dashboard, link them with actions for directed analytics and actionable intelligence)

Manage your own Tableau site (add and remove users, apply security and permissions, determine when workbooks get published and refreshed, answer all questions from your end users, etc...)​​​​​​​

Scroll to top​​​​​​​Scroll to General Queries



Q3 | Does the platform team support custom development on Tableau

Custom Functionality Policies

Tableau is a self-service business intelligence platform.  One of the key ideas behind "self-service" is that the platform is set as to run as smoothly as possible for the 400+ business teams using the platform and that those teams use the platform "as-is".  We do not employ much custom functionality as that limits our ability to upgrade quickly and regularly.  Tableau releases updated software every month and every component of custom built functionality needs to be tested as upgrades occur.

With that said, we do recognize that there are times when additional functionality would be beneficial.  Our team has built a number of custom alerts and other tools which we maintain and which benefit everyone using the platform.  We've had a number of teams ask us for specific custom functionality.  If we feel the functionality will benefit many teams and will not be too complex to maintain, we'll develop it and roll it out for everyone.  However, there is a good chance that we will not agree to develop that functionality.  In those cases, teams can build the functionality themselves.  Tableau allows users to use the REST API, TABCMD utilities, and Tableau SDK for more programmatic access to dashboards and functionality.  If a team decides to build their own functionality, they need to be aware of the following policies:

We do not recommend teams build custom functionality and we will not help teams or provide the software to use TABCMD.  However, we will not stop it either (unless it causes issues with the Tableau server).

Each team is responsible for their own server to run TABCMD and their custom code.

We do not take user-built custom functionality into account during upgrades.

It is the responsibility of the business team to fully test their custom code during upgrades.

We will not consider issues with custom code a "show stopper" or delay upgrades if there are issues.

We will not enable "trusted tickets" to remote servers as part of a request for custom functionality. Trusted tickets compromise security and can allow an unlimited number of users access to the server which could dramatically degrade performance.

You must let us know about any custom functionality you are building at <EMAIL>.

If we determine that your custom code is affecting the performance of the Tableau server in a negative fashion, we will require you to turn off the code. Failure to turn off the code will result in the suspension of the entire site (i.e. no one will have access to the site or dashboards).

Automated publishing events should not be more frequent than once per hour. For example, if you build a data set outside of Tableau, it cannot be published every 15 minutes. 

If you have any questions about these policies or would like to set up a meeting to discuss additional options, please send an email to <EMAIL> Author: Ben Torgesen [btorgese]​​​​​​​

Scroll to top​​​​​​​Scroll to General Queries



Q4 | What is the Cisco Color Palette to be uses in Tableau?

The Cisco Color Palette is defined in the Cisco Brand Document: Our Colors and Gradients.​​​​​​​​​​​​​​You can add a custom color palette to Tableau by editing the Preferences.tps file as described in Tableau Online Help Article: Create Custom Color Palettes.​​​​​​​

For the Cisco Extended Color Palette (excluding white) simply add this to the Preferences.tps file.

<color-palette name="Cisco extended palette" >     <color>#004BAF</color>     <color>#097DBC</color>     <color>#049FD9</color>     <color>#34ADDE</color>     <color>#64BBE3</color>     <color>#C4D6ED</color>     <color>#000000</color>     <color>#39393B</color>     <color>#626469</color>     <color>#9E9EA2</color>     <color>#C6C7CA</color>     <color>#E9E9E9</color>     <color>#E8EBF1</color>     <color>#008516</color>     <color>#00AD0B</color>     <color>#6CC04A</color>     <color>#ABC233</color></color-palette>

Note also these earlier color palette posts:

Cisco Color Palette for Tableau Software

Cisco Color Palette in Tableau​​​​​​​

Attachments: View Folder

Scroll to topScroll to General Queries



Q5 | What are the Recommended Browsers?

The Tableau web environment is based on HTML5 code. No plug-ins (e.g. Flash or Java) are needed. However, a modern HTML5 compliant browser is required. Most of the standard browsers are now HTML5 compliant, but some browsers are more compliant than others.

Here is Tableau's official browser requirements page: http://www.tableausoftware.com/products/online/specs.However, based on our experience we recommend some browsers over others.

Recommended (in order of preference; latest versions of)

Chrome

Safari

Firefox 

Least Recommended

Internet Explorer (IE)​​​​​​​

You can also view the HTML5 compatibility of your favorite browser at http://html5test.com/results/desktop.html. Browsers that are less compatible will work most of the time, but will occasionally show strange behavior. Some of these glitches might include seeing the spinning wait icon for long periods without data refreshing or missing data elements on the page.​​​​​​​

Original Author: Ben Torgesen [btorgese]​​​​​​​

Scroll to top​​​​​​​Scroll to General Queries



Q6 | Date format not working properly while downloading the data from tableau server dashboard

​Go to Control Panel\Clock and Region and then select Region and verify the user also has the same setting.​​​​​​​

Scroll to top​​​​​​​Scroll to General Queries

5 |  Connected to Different Data sources

Q1 Connecting to Splunk from TableauQ2 Connecting to Composite from TableauQ3 Connecting to MapR Hadoop HiveServer2 from Tableau​​​​​​​Q4 Connecting to OneDrive from Tableau​​​​​​​

Q5 Connecting to OracleQ6 Connecting to Oracle from Tableau Desktop on Mac OS​​​​​​​​​​​​​​Q7 Connecting to other Database (ODBC) DB from Tableau​​​​​​​

Q8 Connecting to Salesforce.com from TableauQ9 Connecting to Smartsheet from Tableau​​​​​​​Q10 Connecting to SQL Server from Tableau​​​​​​​

Scroll to top

Q1 | Tableau using Splunk Data​​​​​​​

Note: Most of this information was found here and it still serves as good reference material.​​​​​​​1. First you must make sure your system meets the prerequisites.

a. Ensure you are running Windows 7 or later versions.b. Ensure you have Tableau 8.1 or later versions.c. Navigate to ​​​​​​​​​​​​​​www.microsoft.com and download and install the Microsoft Visual C++ 2010 Redistributable Package.

Disclaimer: This is only possible for users running Tableau on a Windows machine (or virtual machine) at the moment.

2. Next you must install the ODBC driver on your computer. This also gives you the option to connect to Splunk from Excel and MicroStrategy in addition to Tableau. This post will focus on Tableau.​​​​​​​

​​​​​​​a. Navigate to: https://splunkbase.splunk.com/app/1606/ and Login to download the ODBC driver from Splunk. You may have to create a splunk account first in order to download.

b. Unzip the downloaded file and install the 32-bit version of the driver.

c. Accept the license and generally leave the installation location as default.

d. Enter your CEC username, but do not enter your password here. Enter the server URL as http://sra-search-ats.cisco.com .Click next and finish the install.​​​​​​​

e. To verify successful installation, follow these instructions.



3. Now open Tableau and connect to Splunk.

a. ​​​​​​​In the Data Connection screen, click on 'Splunk' under the Server connection options.b. Enter your CEC credentials and the server name and port.c. Click OK.​​​​​​​



4. You will now be connected to the Splunk server and have your saved searches available to use as data sources.

a. Select the search(es) that you want to use as a data source.b. Be sure to select the Extract button so you do not query the Splunk servers every time you make a change to your visualization.c. Create cool visualizations with Splunk data!​​​​​​​​​​​​​​​​​​​​

Original Author: Ben Torgesen [btorgese]

Attachments: View Folder

Scroll to topScroll to Connected Different Data Sources



Q2 | Connecting to Composite from Tableau​​​​​​​

Cisco Information Server - CIS (aka Composite) is a data virtualization tool which lets teams combine data from multiple sources so that it looks like a single database table. This benefits Tableau users greatly as they don't need to complicate their visualizations and dashboards with lots of data sources which require blending.  When you blend multiple data sources in Tableau, additional limitations and performance impacts are introduced.  Teams who have engaged with the CIS team (Enterprise Data Virtualization (Tibco Data Virtualization)) will have already installed the ODBC drivers required to connect to CIS. In addition, they will have used the CIS client to create connections to the original data and define how the data is connected. The final step is to publish the table view definition which makes all of the data from multiple systems appears to be contained in a single table.​​​​​​​

Please note that the new CIS 7.0 ODBC drivers do not work with older versions of Tableau Desktop (before 9.3.1). There are 2 Options that do work:

Install Tableau Desktop 9.3.1 and use the new Cisco Information Server native data source which does work with the CIS 7.0 ODBC drivers.

Continue to use the Composite 6.2 ODBC drivers with earlier versions of Tableau Desktop. The data source configuration instructions for this option are below: 

Connecting to CIS to view the data within Tableau is as simple as choosing an "Other Database (ODBC)" connection type. Select the "Driver" option instead of "DSN", then choose the CIS (or Composite) driver (currently Composite 6.2) and then click "Connect".

When presented with the Composite ODBC Driver dialog box, most of the information you enter will come from how you use Composite (i.e. which environment/server you're using, your account name and password, your group name, your published data, etc...).  However, there is one unique element that you need for Tableau to work.  In the first entry (DSN Name:), you must enter a single space. If you type an actual name or leave it empty, it won't work.  Just press the space bar once and then continue to the other fields.

Once you press OK, the information will be transferred to the "Connections Attributes" section and you can click OK again to connect.

Original Author: Ben Torgesen [btorgese]​​​​​​​

Attachments: View Folder

Scroll to topScroll to Connected Different Data Sources



Q3 | Connecting to MapR Hadoop HiveServer2 from Tableau​​​​​​​

Updating Existing HiveServer Connections

Creating Brand New HiveServer2 Connections

MapR Hadoop Hive Upgrade (migration from HiveServer to HiveServer2)​​​​​​Hive is one of the most common ways to query data stored in Hadoop. Some teams may still have connections to the original HiveServer and need to upgrade to HiveServer2. Most teams have been using HiveServer2 connections for some time. However, the Hadoop team has rolled out load balancing for HiveServer2 connections and the Hadoop server name may have changed recently.Please see the Hadoop Jive post for information about how to use the load balancer.Note: the server names in the screenshots below are for development and don't use the new load balanced server names.

Original Author: Ben Torgesen [btorgese]​​​​​​​

Attachments: View Folder

Scroll to top​​​​​​​Scroll to Connected Different Data Sources



Updating Existing HiveServer Connections

Most teams currently using Tableau with Hadoop are connecting over a HiveServer connection. These teams will need to upgrade all of their workbooks (or saved data sources). Please follow the steps below to change the connection for each workbook (or saved data source):

1. Tableau Server does have the ability to change many elements of a connection directly on the server. These include server names, port numbers, user credentials, etc... It would have been a quick change for everyone if this functionality included the ability to change the HiveServer type (i.e. from 1 to 2). However, this ability does not exist. This requires the change to take place on your local computer using Tableau Desktop.

 

 

<image1>

2. For each workbook (or saved data source), you'll need to locate your local (official) version of the file. If you do not have an "offical" copy of the file, you'll need to download the production workbook (or data source) from the Tableau server.

 

3. Open the file (either a .twb, .twbx, .tds, or .tdsx file), and then edit the data source. Choose Data -> <data source name> -> Edit Data Source...​​​​​​​

 

<image2>

4. Click on the orange server name and the MapR Hadoop Hive connection dialog box will open up. You'll see the server name and port number for a HiveServer instance with no credentials.​​​​​​​​​​​​​​

​​​​​​​<image3>​​​​​​​

5. You will need to change the port number to the appropriate HiveServer2 port (most likely 20000), then change the Type: to HiveServer2 and the Authentication: to User Name and Password. At this point, you'll be able to enter the user credentials that the Hadoop team has provided to you. Click OK and you should be done with that data source.

​​​​​​​<image4>

6. If your workbook (or saved data source) has multiple data source connections to Hadoop, you'll need to make the same change for each one.​​​​​​​

7. At this point, you can republish your workbook to the Tableau server and the upgrade to HiveServer2 is complete. If you have been upgrading a data source which has been saved to the server, you should be able to republish that data source and all of the workbooks which have been built off of that data will automatically reflect the change. Make sure you're prompted to "overwrite" the existing data source on the server so that you don't have to edit any of the workbooks based on that data source.

Scroll to topScroll to Connected Different Data Sources



Creating Brand New HiveServer2 Connections

There is a glitch with Tableau (or the ODBC drivers) with regards to HiveServer2 and Cisco's security implementation. After you connect to the Hadoop edge node, you should be able to select the schema (or Hive database) and the table. However, while viewing the list of schemas will work fine, you probably won't be able to view any of the tables within the chosen schema. This is true whether you search for a specific name or just click the magnifying glass to see all of the names.​​​​​​​

 

 

<image5>

There is an extra step to make sure the tables become visible. When you're at the connection dialog box, you need to click the orange "Initial SQL..." link in the bottom left corner. When the Initial SQL dialog box opens, enter "use <database name>" where <database name> is the name of a Hive database to which you have access. In the example below, we entered "use reference" as the reference database has lots of reference tables.​​​​​​​

 

 

<image6>

Click "OK" twice and then you'll be able to see all of the tables in the schema (or Hive database). If you change the schema in the Tableau drop-down menu, you do not need to change the initial SQL. It only needs to be set once for any database to which you have access.

Original Author: Ben Torgesen [btorgese]

Attachments: View Folder 

Scroll to topScroll to Connected Different Data Sources



Q4 | Connecting to OneDrive from Tableau​​​​​​​

Open Tableau Desktop.

Connet to OneDrive and SharePoint Online



Authentication window will open to authenticate → Login With your Cisco Credentials.



Upon successful authentication OneDrive and SharePoint is connected. 



​​​​​​​

Scroll to top​​​​​​​Scroll to Connected Different Data Sources



Q5 | Enterprise Tableau Oracle Connection​​​​​​​

Oracle databases are one of the most common data sources at Cisco.  There are a variety of ways that you can create a connection to Oracle using Tableau that affect both the Desktop software as well as the server software.  We'll outline three different options and point out the preferred method.​​​​​​​

First, you must make sure that the Tableau Oracle drivers from the Tableau Software web site are installed.

1. Database Name Only (Preferred Option)

​​​​​​​You need to have a Cisco version of Oracle client software installed.  The client install will install a file called TNSnames.ora which allows you to simply use the name of the database in the Oracle Connection dialog box. The TNS server is able to resolve all of the needed connectivity information with just the database name. With this method the "Server:" field is the name of the database.

 

The primary benefits of this method are simplicity and the fact that the database can change hosts and be upgraded without needing to change your workbooks.​​​​​​​



Possible error: If you do not have the Oracle client correctly installed with a valid TNSnames.ora file, you will receive the following error if you try to connect with this method. One solution is to use option 2 below.





2. Fully Defined Connection (Optional method if you do not want to install the Oracle client -OR- If you are using a Mac)

You need to fully define the Oracle connection using the two optional fields.  In addition to the database name, you also need the host name and the database port number. You can either ask your DBA contact for the additional information or you may be able to look it up here (http://dbatool-prod.cisco.com:7777/cgi-bin/tnsping.pl).  With this method, the "Server:" field is the host name and the "Service:" field is the name of the database with ".cisco.com" added to the end.

 

One limitation of this option is that you will need to update the hostname and/or port number if those change during a database upgrade or migration. These updates can take place directly on Tableau Server for multiple workbooks using the same data source (Tableau Server - Edit Data Source Connections).​​​​​​​



Possible Error: If you are using a Mac, you may get the following error. If you do, please see this best practice post (Tableau Desktop for Mac - Oracle DB connection).​​​​​​​





3. ODBC DSN Name | Do not Use this Method

Define the Oracle data source using the ODBC Data Source Administrator program in the Windows Control Panel.  This method also allows for simply using the ODBC name when pointing to a database.

Problems with this method include the following:

This only works for workbooks used exclusively with the Desktop software.  When the workbook is published to the server, the ODBC connection will not exist on the server.  This means that any extracts or live connections will fail.



Scroll to top​​​​​​​Scroll to Connected Different Data Sources



Q6 | Tableau Desktop for Mac Oracle DB connection

There are some potential issues getting the Tableau Desktop for Mac software to connect to internal Oracle databases.  There may be a couple of extra steps needed.  Here’s the simplest way to get the Oracle drivers working on Mac OS X in Cisco.  I’ll paste the Tableau instructions and then add my own (in red).​​​​​​​

Close Tableau Desktop.

Download the drivers from the Download link (Driver Download | Tableau Software - scroll down to Oracle and select "Download Mac")

Double-click the downloaded file, TableauDrivers.dmg.

Double-click Tableau Oracle Libraries.

Accept the defaults.

Open Tableau Desktop and connect to Oracle.Some people have still reported problems after following the above instructions. If you're still unable to connect, please follow these additional instructions. 

Open terminal

Find out your exact hostname by typing “hostname” at the prompt

Become the root user by typing "sudo su" and entering your password, then edit the hosts file by typing “vi /etc/hosts”

At Cisco the hosts file looks like…### Host Database## localhost is used to configure the loopback interface# when the system is booting.  Do not change this entry.##127.0.0.1       localhost localhost.cisco.com255.255.255.255 broadcasthost::1             localhostfe80::1%lo0     localhost# request from CSIRT to point mac-wiki.com to mac-wiki.cisco.com IP172.18.106.82   mac-wiki.com

Add your full hostname to the end of the 127.0.0.1 line.  For me it looks like…127.0.0.1       localhost localhost.cisco.com BTORGESE-M-20DF.CISCO.COM

Reboot your computer.

Open Tableau Desktop and connect to Oracle. 

Original Author: Ben Torgesen [btorgese]

​​​​​​​Attachments: View Folder​​​​​​​​​​​​​​

Scroll to topScroll to Connected Different Data Sources



Q7 | Enterprise Tableau ODBC Connections

While Tableau has dozens of native connections to popular data sources (Oracle, SQL Server, Hadoop, Salesforce, etc...), it cannot possibly account for the thousands of database types that exist. To remedy this, Tableau does support industry standard ODBC connections. Most database vendors will make sure that their databases are ODBC-compliant for this very reason. Two of the most popular data sources at Cisco which require an ODBC connection to work with Tableau are CIS (aka Composite) and Smartsheet. Since CIS is a Cisco product, we have a separate post on how to configure a connection to this source. While this post will include screenshots for Smartsheet, the same principles apply to any ODBC connection.​​​​​​​

There is a standard way that ODBC connection are typically configured. Using the Windows ODBC Administrator tool, people define a data source connection as a DSN entry and then they can refer to that DSN in any tool that needs a connection to that data source​​​​​​​​​​​​​​​​​​​​​



This approach DOES NOT WORK with Tableau. The key reason is that the DSN entry that has been defined on your computer, does not exist on the Tableau Servers. If you only view your workbooks locally and never publish to the server, then you can use this approach. However, if you publish a workbook using this method to the Tableau Server, the DSN entry won't exist on the server and the connection will fail.​​​​​​​

Fortunately, there is a way to use an ODBC connection with Tableau. When you click on "Other Databases (ODBC)" as your data source, you have the choice of either a DSN connection (BAD) or a Driver connection (GOOD). When you select the Driver option, you will need to choose the data source type to which you're trying to connect. If you have properly installed the ODBC drivers for your data source prior to this point, the driver name should appear in the drop down list. Once you choose the name and click connect, you'll be prompted for all of the information related to this data source.​​​​​​​​​​​​​​​​​​​​​



When you define an ODBC connection this way, all of the information needed to connect to the data source is saved with the Tableau workbook and is also published to the server. Please don't forget to embed your credentials when you publish to the server so that the connection can be completed.

Our team does need to install the ODBC drivers for your unique data sources. We have a number of ODBC drivers already installed (e.g. Attivio, Smartsheet, CIS, etc...). If you feel that drivers you need are missing, please open a case for us to evaluate your request here: http://go2.cisco.com/TableauSupport 

Original Author: Ben Torgesen [btorgese]

​​​​​​​Attachments: View Folder​​​​​​​​​​​​​​

Scroll to topScroll to Connected Different Data Sources



Q8 | Salesforce.com connector within Tableau​​​​​​​

Tableau Desktop and Server allow direct connections to Salesforce.com to extract data.  Here is an example of the connection dialog box...​​​​​​​​​​​​​​​​​​​​​

Unfortunately, the Cisco SFDC administrators have turned off the ability to query data directly from SFDC. The queries coming from Tableau and other BI tools were causing too many performance problems. With this in mind, the GBS organization has created a database which houses much of the data found in SFDC. It is updated daily and you can request access.​​​<image> (Image unavailable)

Sales CRM Community page

Direct link to their engagement template - Work Request Status​​​​​​​

While they have already brought a lot of data from SFDC into the local Cisco database, it is possible that your data may not have been loaded. During the engagement process, you'll need to share which SFDC pages contain the data you'd like to load. The database team will evaluate if it is available. If it is not available, there will be a schedule to bring the data into the database and a discussion about cost.

Once you have access to the database, you'll be able to query your required information as from any database.​​​​​​​

Original Author: Ben Torgesen [btorgese]

​​​​​​​Attachments: View Folder 

Scroll to topScroll to Connected Different Data Sources



Q9 | Smartsheet ODBC Connectivity​​​​​​​

Please note: The Smartsheet ODBC drivers are only available for Windows. You cannot connect to Smartsheet using Tableau Desktop on your Mac.

While Smartsheet is accessed with a standard ODBC connection (which is documented here), there are some unique requirements to use Smartsheet. These include:​​​​​​​

Generating an API Access Token

Installing the correct version of the Smartsheet drivers

Creating the ODBC connection in Tableau and using the API Access Token

Using the API Access Token as the password when you reopen a workbook or data source

Detailed instructions for each step can be found below:​​​​​​​1 | Since Cisco uses single sign-on to access Smartsheet, you can't use your username and password to access Smartsheet data from Tableau. You must generated an API Access Token to be able to query your data. The following is from the Smartsheet help pages.

Generate an API Access Token (for SSO users).​​​​​​​

First, you need to create a token - it’s easy with just a couple of clicks. In Smartsheet, navigate to Account > Personal Settings:



In Personal Settings, select “API Access” and click on “Generate new access token”:



Give the new token an arbitrary, descriptive name and click “OK”:



Make sure you copy the token before you close the window because you won’t be able to view the token again (for security reasons).



2 | The Smartsheet ODBC driver version available below is the same version installed on our enterprise Tableau servers. While newer versions found on their website will likely work, there is the possibility that issues may occur. We recommend using the same version installed on our enterprise servers.

Smartsheet ODBC drivers

3) Create the ODBC connection in Tableau Desktop using the Driver: option instead of DSN:

Connect to the Smartsheet Data Source by choosing the “To a server” > “Other Databases (ODBC)” option. If you do not see the ODBC option listed here, click on the “More Servers…” and choose it from the list from additional options:



Under “Connect Using” pick “Driver”, and then select “Smartsheet Live Data Connector” from the Driver drop-down menu:​​​​​​​… and click “Connect”.



Connect to Smartsheet by clicking on the “Use API token for SSO login” option:



… and paste your access token:



Upon successful login, confirm connection information:



4) Since Tableau Desktop requires users to renter credentials when opening a saved workbook or data source, you will be prompted for your API Access Token whenever you reopen a Smartsheet based Tableau file. Please enter the token in the password field.

Original Author: Ben Torgesen [btorgese]​​​​​​​

Scroll to topScroll to Connected Different Data Sources



Q10 | Microsoft SQL Server Connectivity​​​​​​​

Lots of business groups use SQL Server at Cisco. However, due to some of the most commonly used security settings, building and publishing workbooks with Tableau can be difficult. The key problem is how SQL Server handles user authentication. Since SQL Server is a Microsoft product, they recommend using "Windows Authentication" as the preferred method.​​​​​​​



This option passes the Windows OS username to the database as it assumes you've already been authenticated within the Windows security domain. Most SQL Server database administrators set up Windows Authentication as the only option for connecting to the database. The problem inherent in this option is that while this works fine using Tableau Desktop, it does not work once a workbook is published to the server. The Windows OS username for the Tableau servers is "tabadmin.gen" and not the person who developed the dashboard. This causes most workbooks using SQL Server to fail when either connecting with a "live" connection or when refreshing a data extract within the workbook.​​​​​​​​​​​​​​

There are two options to enable SQL Server connectivity from the enterprise Tableau servers:

Grant SELECT (or read-only) access for the tables and data structures your team is using within your SQL Server database to the user "tabadmin.gen". This option will allow your workbooks to function whether they are on your desktop using your username or on the Tableau server using "tabadmin.gen".

Have your SQL Server database administrator turn on "specific username and password" functionality and create an account with the correct access for the workbook developer. This would allow the developer to embed the specific username and password in the workbook when it is being published to Tableau Server. The server would then utilize this specific account for refreshing data from the database.

Original Author: Ben Torgesen [btorgese]

Attachments: View Folder 

Scroll to top​​​​​​​Scroll to Connected Different Data Sources

6 |  Access to Tableau/ Issues

Q1 How do I request for a Tableau Site to share workbooks?​​​​​​​Q2 I need access to an existing Tableau Site. Whom should I reach out to?​​​​​​​​​​​​​​

Q3  I am unable to see any data in the dashboard​​​​​​​​​​​​​​​​​​​​​

Q4 I am unable to view a particular dashboard but I am able to open other dashboards in the site.​​​​​​​

Scroll to top

Q1 | How do I request for a Tableau Site to share workbooks?

You can raise a request for production account from eDNA. Please follow the steps mentioned below.



Create a new site on Enterprise Tableau Server

How to Set Up Dev/Stg Site?​​​​​​​

​Go to edna.cisco.com and sign in (cec)

Go to the Data Platforms > Tableau > Project Onboarding Account

Fill out all the details and hit submit.

Once submitted, it will go for approval to your manager and the Tableau Platform team

Once approved by both, the site will be created in Tableau Dev and Stage Environments.

​​​​​​​Promoting site to Production

Once the site has been created in Dev and Stage environment, you need to promote the site to production.

Go to edna.cisco.com and sign in (cec)

Go to Requests & Approvals > Requests > Applications & Accounts

Select your application, Go to the Stage Tab & then hit "Promote to Prod". Refer to sample image below.

Once promoted to prod, it will raise another approval from Manager and the Tech team.

Once approved by both, the site will be created in Tableau Prod Environment. 

​​​​​​​Note: We are working with the eDNA team to unify both the process so only single approval will be needed and the site will be created in all the 3 environments.



Scroll to top​​​​​​​Scroll to Access to Tableau/ Issues



Q2 | I need access to an existing Tableau Site. Whom should I reach out to?​​​​​​​​​​​​​​

​​To obtain access to an existing tableau site, please open a ticket to enable  Viewer/Explorer  access, ensuring that the ticket includes an approval comment from one of the site administrators. and user manager 

 

And also, include the direct URL of the Tableau dashboard that your trying to access in the ticket notes​​​​​​​ 

Scroll to top​​​​​​​Scroll to Access to Tableau/ Issues



Q3 | I am unable to see any data in the dashboard

​It can be due to an access issue or due to the ​unavailability of data. Such issues can only be addressed by Workbook Owner. You can find the workbook owner in the top left corner when you open the workbook (please refer to the attached image for reference).​​​​​​​

Scroll to top​​​​​​​Scroll to Access to Tableau/ Issues



Q4 | I am unable to view a particular dashboard but I am able to open other dashboards in the site.

​It can be due to permission issues. Such issues can only be addressed by Workbook Owner or the Project Owner. You can find the workbook owner in the top left corner when you open the workbook (please refer to the attached image for reference).​​​​​​​

Scroll to top​​​​​​​Scroll to Access to Tableau/ Issues​​​​​​​

7 |  Performance Optimization Tips & Tricks

Q1  What are the Tableau Performance Hints and Tips?​​​​​​​Q2  How to Optimize Data Extracts?​​​​​​​​​​​​​​

Q3  Why Enterprise Large tables of text are bad in Tableau?​​​​​​​Q4  How to create Enterprise Tableau Empty Desktop Extracts?​​​​​​​​​​​​​​​​​​​​​

Q5  Tableau Performance Tricks - Detailed

​​​​​​​Scroll to top

Q1 | What are the Tableau Performance Hints and Tips?

There are a million ways to optimize performance in Tableau. Many customer conference session are devoted to this topic each year. One of the key elements to remember is that you have to think about performance as you're building your workbooks. If you wait until a workbook is complete and then you try to figure out why it is so slow, it will be very difficult. Here are a few suggestions for things to look at when you're trying to make a dashboard faster:

Don't try to make Tableau be like Excel with tons of quick filters. If your client/business team really wants raw data with 20 filters, then just send ‘em a spreadsheet, and be on your way to someone that wants visualization.

Never drag any Data Field you don’t need into Detail or any other Shelf of the Viz. Every field used, even if only in Detail, massively complicates the resulting vizql queries as the user interacts with the viz. Use only what you must, and nothing more.

In lieu of Quick Filters, you would be better off making lots of small Worksheets that enumerate the values of one and only field each, with only one Quick Filter for that one field.  Then, assemble those together into a Dashboard, and use each View as a Filter.  (http://kb.tableau.com/articles/knowledgebase/creating-filter-actions-dashboards )

Keep your data set no bigger than is absolutely necessary. Omit needless rows. Omit needless fields.  When you’re done, then “Hide Unused Fields” should have no effect. Until then, keep chopping unnecessary fields out of your Data Source.

Do not use Data Blending, if you care about speed / performance. Get your data sources merged upstream. Feed Tableau only a single, clean, fat / flat source, if you want it to be at its best.

Extract your data source(s) if you can, and Optimize the Extract(s) if you have per-Row Calculated Fields.

Although Level-of-Detail (LOD) Calculated Fields are extremely helpful at producing useful results, they do come at a performance cost since they force Tableau to scan every record in order to do the computation. If you have a lot of LOD calcs, consider finding a way for those results to be computed and provided in an upstream Data Source.

If any of the source data is hierarchical, then experiment with Hierarchies in Tableau. Depending on the cardinality of the data fields in the Hierarchy, you may see a performance boost (if low cardinality in the topmost fields in the hierarchy), or a performance penalty (if high cardinality in the topmost fields in the hierarchy).

Consider using Sets (which are basically a pre-fabricated combination of filters) for selections of Filter combinations you expect your users are most likely to use.

Where possible, use Continuous Dates and Measures instead of Discrete. With Continuous, Tableau only needs to know the MIN and MAX of the field (which are pre-computed in the Extract) in order to render that field in the viz. With Discrete, it has to retrieve every single value of the field, from every single record.

Don’t keep any unneeded (e.g. “scratchpad”) Worksheets in the final Workbook. Keep two copies of the Workbook – a dev copy with your scratchpad Worksheets in it, and a Publish-ready copy that only has required Worksheets and Dashboards in it.

If you are using Data Blending (against advice), then consider using a Parameter to filter across multiple Data Sources, instead of blending the Data Sources together. While not always feasible, a Parameter, driving one Calculated Field per Data Source, provides a more performant way to filter across Data Sources then would Blending them and then applying a Quick Filter.

Never use a high-cardinality discrete field as the basis for Color. Doing so bloats up the size of the XML that binds a Color to each Value. (For example, don’t color by Customer.) 

Beyond these, there are some more sophisticated and complicated things you can get into, but they are likely not worth it.

As for the Tooltip, never let that be your guide. It is designed, by default, to only surface the values tied to a single Mark. If you’re trying to test the efficacy of the filters you have, then you need to build some scratchpad worksheets that let you sanity check against things like SUM and Number of Records.Thanks to Ken Patton (kpatton) for this great write up

Original Author: Ben Torgesen [btorgese]

Scroll to topScroll to Performance Optimization Tips & Tricks



Q2 | How to Optimize Data Extracts?

Enterprise Tableau Data Extracts​​​​​​​Extracting DataExtracts are saved subsets of a data source that you can use to improve performance, upgrade your data to allow for more advanced capabilities, and analyze offline. You can create an extract by defining filters and limits that include the data you want in the extract. After you create an extract you can refresh it with data from the original data source. You can either fully refresh the data, replacing all of the extract contents; or you can increment the extract; which only adds rows that are new since the last refresh.

Extracts can

Improve performance. For file based data sources such as Excel or Access, a full extract takes advantage of the Tableau data engine. For large data sources, a filtered extract can limit the load on the server when you only need a subset of data.

Add functionality to file based data sources, such as the ability to compute Count Distinct.

Provide offline access to your data. If you are traveling and need to access your data offline, you can extract the relevant data to a local data source 

Optimizing Extracts1 | To improve performance when working with extracts you can optimize the extract. Optimizing moves the calculated fields you’ve created into the extract so that they don’t have to be computed locally every time you use them.

Optimize the extract by selecting a data source on the Data menu and then selecting Extract > Optimize.

When you modify the calculated field, the modified version will be used until you optimize the extract again.

Each time you optimize the extract, any deleted calculations will be removed fro the extract, new ones will be added, and modified ones will be updated. 

2 | Another option to minimize the size and processing time of an extract is to hide all unused fields.  Within the data source list on the left of the screen, you can hide individual fields that are not important or not being used.  You can also choose to "Hide All Unused Fields" within the Extract Data dialog box.



Hiding fields can be a good way to decrease the size of a data extract file because hidden fields are automatically excluded from the extract.

3 | Selecting the "Aggregate data for visible dimensions" is one of the best ways to minimize data.  With this option selected, Tableau will aggregate and group by the lowest levels of data that you actually use in your visualizations.  For example, your data source might include the entire product hierarchy from Technology Group down to Product ID.  However, if your visualizations only show down to Product Family, choosing the "Aggregate data" option will sum (or average) all of the lower level data to the Product Family level.  This could result in a 100:1 or 1000:1 reduction in your data set.  Also, If you have any date/time columns in your data, please select "Roll up dates to".  Your charts probably don't include analysis based on seconds, minutes, or hours.  If you can choose a higher level in the date hierarchy (perhaps days or months), the extract will be much smaller and you'll still be able to visualize your data.  These settings will also dramatically increase the speed of your dashboards.

4 | Incremental extracts should also be used when possible.  If there is a unique key (such as date or rowid) which is always incrementing, then an incremental extract will save lots of processing time.  Only the new data created since the last extract will need to be queried and processed.  With a full extract, the full data set has to be queried and processed each time.  One circumstance that would require full extracts, would be if the historical data changed or was restated regularly. 

Extract Sizes and DurationsGroups are able to create extracts for either workbooks or server-based data sources.  Extracts are not meant to replicate an entire data source or extrememly low-level detail information.  Extracts are also not meant to pull in hundreds of columns of attributes.  Extracts work best when they are pulling summary aggregated information.  With that said, we also have some recommendations and limits regarding the size of extracts on the enterprise Tableau server. 

Duration:

7200 seconds (2 hours) - This is a limit currently set in the configuration files for the Tableau server.  Any jobs running longer than two hours will be terminated.

Size:

500 MB - This is a hard limit.  It corresponds to about 5 GB of raw, uncompressed data.  Depending on the number of columns in the extract, it could should be somewhere between 1 and 10 million rows of data.



3. If you are a site administrator you can also click on the "Status" tab and select the "Stats for Space Usage" dashboard. This report will show you the size of all of your workbooks and data sources. It will also provide summary analytics based on individuals and projects.



Original Author: Ben Torgesen [btorgese]

Attachments: View Folder

Scroll to topScroll to Performance Optimization Tips & Tricks



Q3 | Why Enterprise Large tables of text are bad in Tableau?

Tableau is a data visualization tool.  It should not be considered "Excel-on-the-web".  No dashboard or report should ever be deployed where more than 5,000 - 10,000 rows of data (text) can be displayed.  Users are not able to scroll though thousands of rows of data and make any sense of the information you're trying to convey.

If you need to allow users to download detailed data, please train them to hoover over a data element and to select the "View data" icon.



They can then click on the "Underlying" tab to get the details and download a CSV file with all of the rows of data.  You do not need to have a table of with hundreds of thousands of rows of data being rendered to the screen.



In addition, there are ways to limit the data shown in a cross-tab before the data is displayed. Thoughtful visualizations can help prompt users to filter on critical areas before the details are displayed. The following embedded dashboard shows how this can be done. In addition, the workbook has been saved below for anyone to examine. The file is a .zip file to comply with WebEx Social requirements, but unzips to a .twbx file.

Click Here to see a Tableau Dashboard example of how to enable details.

Original Author: Ben Torgesen [btorgese]

Attachments: View Folder

Scroll to top​​​​​​​Scroll to Performance Optimization Tips & Tricks



Q4 | How to create Enterprise Tableau Empty Desktop Extracts?

Enterprise Tableau Empty Desktop Extracts​​​​​​​

Empty ExtractsSometimes extracts will either take too long or be too large to deal with effectively in the Desktop software.  This is especially true if the extract needs to be created or a workbook needs to be published to the server and the user is in a remote location or on a slow internet connection.  Since extracts can be quite large (hundreds of megabytes), these operations can be very difficult.  There is a special trick which allows the user to create an empty extract on their desktop which results in a very small file size.  The extract can then be uploaded to the server where the full extract can run.​​​​​​​

Step 1  Create a calculated column with the formula of "Now()".

Step 2  Log into the server where you are going to publish the workbook.



Step 3  Right-click on the new calculated field and select "Describe...".  Note down the date and time shown.  It might not be your time zone.



Step 4  Click on the Data->Extract Data menu item to bring up the "Extract Data" dialog box.  "Add..." a new filter and choose the newly created calculated field as your choice.  When prompted about "How do you want to filter on [column name]?, choose the "Range of Dates" option and then click on the "Starting date" box.  Now, simply change the starting date to a time in the future (15-20 minutes should be good).  Click OK and then click the Extract button in the "Extract Data" dialog box.



Step 5  At this point, all of the data in your worksheets and dashboards will have vanished.  The extract that those reports point to is empty.  Save the workbook and then publish it to the server.  Once the starting date/time from the filter has passed, you can run the extract on the server and all of the data will then be available.

Original Author: Ben Torgesen [btorgese]

Attachments: View Folder

Scroll to topScroll to Performance Optimization Tips & Tricks



Q5 | Tableau Performance Tricks​​​​​​​​​​​​​​​​​​​​​

​​​​​​​

Follow this link for the detailed solution:​​​​​​​https://cisco.sharepoint.com/Sites/CiscoTableau/Lists/TestList/DispForm.aspx?ID=67&e=CP1Bvl



