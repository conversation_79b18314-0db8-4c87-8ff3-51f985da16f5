#!/usr/bin/env node

/**
 * Document Extraction Script
 * 
 * This script extracts text from all documents in the docs directory and saves it to text files.
 * It can be run separately from the main server.
 * 
 * Usage: node src/extract-all-documents.js
 */

const fs = require('fs');
const path = require('path');
const mammoth = require('mammoth');

// Directory containing documents to process
const DOCS_DIR = path.join(process.cwd(), 'docs');
// Directory to save extracted text
const OUTPUT_DIR = path.join(process.cwd(), 'src', 'data', 'extracted');

// Function to find all .docx files recursively
async function findDocxFiles(dir) {
  const files = [];
  
  // Read all files in the directory
  const entries = fs.readdirSync(dir, { withFileTypes: true });
  
  for (const entry of entries) {
    const fullPath = path.join(dir, entry.name);
    
    if (entry.isDirectory()) {
      // Recursively search subdirectories
      const subFiles = await findDocxFiles(fullPath);
      files.push(...subFiles);
    } else if (entry.isFile() && path.extname(entry.name).toLowerCase() === '.docx' && !entry.name.startsWith('~$')) {
      // Add .docx files to the list (skip temp files)
      files.push(fullPath);
    }
  }
  
  return files;
}

// Function to extract text from a .docx file
async function extractTextFromDocx(filePath) {
  try {
    const result = await mammoth.extractRawText({ path: filePath });
    return result.value;
  } catch (error) {
    console.error(`Error extracting text from ${filePath}:`, error);
    throw new Error(`Failed to extract text from document: ${error.message}`);
  }
}

// Function to sanitize filename
function sanitizeFilename(filename) {
  return filename.replace(/[^a-z0-9]/gi, '_').toLowerCase();
}

// Function to run document extraction
async function extractDocuments() {
  try {
    console.log('Starting document extraction process...');
    
    // Create output directory if it doesn't exist
    if (!fs.existsSync(OUTPUT_DIR)) {
      fs.mkdirSync(OUTPUT_DIR, { recursive: true });
    }
    
    // Find all .docx files recursively
    const files = await findDocxFiles(DOCS_DIR);
    console.log(`Found ${files.length} .docx files in ${DOCS_DIR}`);
    
    // Process each file
    const results = [];
    for (const filePath of files) {
      try {
        const fileName = path.basename(filePath);
        console.log(`Processing ${fileName}...`);
        
        // Extract text from document
        const text = await extractTextFromDocx(filePath);
        console.log(`  Extracted ${text.length} characters`);
        
        // Create output filename
        const outputFileName = sanitizeFilename(fileName.replace('.docx', '')) + '.txt';
        const outputPath = path.join(OUTPUT_DIR, outputFileName);
        
        // Save text to file
        fs.writeFileSync(outputPath, text);
        console.log(`  Saved to ${outputPath}`);
        
        // Create metadata
        const metadata = {
          fileName,
          filePath,
          outputPath,
          extractedAt: new Date().toISOString(),
          characters: text.length,
        };
        
        // Add to results
        results.push(metadata);
      } catch (error) {
        console.error(`Failed to process ${filePath}:`, error);
      }
    }
    
    // Save metadata
    const metadataPath = path.join(OUTPUT_DIR, 'metadata.json');
    fs.writeFileSync(metadataPath, JSON.stringify(results, null, 2));
    console.log(`Saved metadata to ${metadataPath}`);
    
    // Print summary
    console.log('\nDocument Extraction Summary:');
    console.log('----------------------------');
    results.forEach(result => {
      console.log(`${result.fileName}: ${result.characters} characters`);
    });
    console.log('----------------------------');
    console.log(`Total files processed: ${results.length}`);
    console.log(`Total characters: ${results.reduce((sum, result) => sum + result.characters, 0)}`);
    
    console.log('Document extraction completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('Document extraction failed:', error);
    process.exit(1);
  }
}

// Run the extraction process
extractDocuments();
