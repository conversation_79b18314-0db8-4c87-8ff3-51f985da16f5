/**
 * <PERSON><PERSON>t to extract text from the "Access to BO Tools via Citrix.docx" file
 * and save it for use in the LLM prompt
 */

const fs = require('fs');
const path = require('path');
const mammoth = require('mammoth');

const filePath = path.join(process.cwd(), 'docs', 'BO Docs Vector DB', 'Access to BO Tools via Citrix.docx');
const outputPath = path.join(process.cwd(), 'src', 'data', 'bo-citrix-content.txt');

async function extractText() {
  try {
    console.log(`Extracting text from ${filePath}`);
    
    // Create the data directory if it doesn't exist
    const dataDir = path.join(process.cwd(), 'src', 'data');
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }
    
    // Extract text from the document
    const result = await mammoth.extractRawText({ path: filePath });
    const text = result.value;
    
    // Save the text to a file
    fs.writeFileSync(outputPath, text);
    
    console.log(`Text extracted and saved to ${outputPath}`);
    console.log(`Text length: ${text.length} characters`);
    
    // Print a preview of the text
    console.log('\nPreview:');
    console.log(text.substring(0, 500) + '...');
  } catch (error) {
    console.error('Error extracting text:', error);
  }
}

// Run the extraction
extractText();
