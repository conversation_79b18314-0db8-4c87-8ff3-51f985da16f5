const express = require('express');
const cors = require('cors');
const morgan = require('morgan');
const config = require('./config');
const routes = require('./routes');
const { getVectorDbService } = require('./services/vectordb');
const { getDocumentIngestionService } = require('./services/document');

// Initialize Express app
const app = express();

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(morgan(config.server.env === 'development' ? 'dev' : 'combined'));

// Routes
app.use('/api', routes);

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(err.status || 500).json({
    error: {
      message: err.message || 'Internal Server Error',
      ...(config.server.env === 'development' && { stack: err.stack }),
    },
  });
});

// Initialize services and start server
const PORT = config.server.port;

// Function to initialize services and start server
async function startServer() {
  try {
    // Initialize vector database service
    const vectorDbService = await getVectorDbService();

    // Initialize document ingestion service if not skipped
    if (process.env.SKIP_DOCUMENT_PROCESSING !== 'true') {
      console.log('Initializing document ingestion service...');
      const documentIngestionService = getDocumentIngestionService();
      await documentIngestionService.initialize();
    } else {
      console.log('Skipping document processing as per configuration');
    }

    // Start the server
    app.listen(PORT, () => {
      console.log(`Server running on port ${PORT} in ${config.server.env} mode`);
    });
  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Start the server
startServer();

module.exports = app;
