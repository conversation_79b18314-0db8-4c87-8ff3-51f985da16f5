#!/usr/bin/env node

/**
 * Document Ingestion Script
 * 
 * This script processes all documents in the docs directory and stores them in the vector database.
 * It can be run separately from the main server.
 * 
 * Usage: node src/ingest-all-documents.js
 */

const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const mammoth = require('mammoth');
const { getEmbeddingService } = require('./services/embedding');
const { getVectorDbService } = require('./services/vectordb');

// Directory containing documents to process
const DOCS_DIR = path.join(process.cwd(), 'docs');

// Function to find all .docx files recursively
async function findDocxFiles(dir) {
  const files = [];
  
  // Read all files in the directory
  const entries = fs.readdirSync(dir, { withFileTypes: true });
  
  for (const entry of entries) {
    const fullPath = path.join(dir, entry.name);
    
    if (entry.isDirectory()) {
      // Recursively search subdirectories
      const subFiles = await findDocxFiles(fullPath);
      files.push(...subFiles);
    } else if (entry.isFile() && path.extname(entry.name).toLowerCase() === '.docx') {
      // Add .docx files to the list
      files.push(fullPath);
    }
  }
  
  return files;
}

// Function to extract text from a .docx file
async function extractTextFromDocx(filePath) {
  try {
    const result = await mammoth.extractRawText({ path: filePath });
    return result.value;
  } catch (error) {
    console.error(`Error extracting text from ${filePath}:`, error);
    throw new Error(`Failed to extract text from document: ${error.message}`);
  }
}

// Function to split text into chunks with overlap
function chunkText(text, chunkSize = 4000, chunkOverlap = 400) {
  const chunks = [];
  let startIndex = 0;

  while (startIndex < text.length) {
    const endIndex = Math.min(startIndex + chunkSize, text.length);
    chunks.push(text.slice(startIndex, endIndex));
    startIndex = endIndex - chunkOverlap;

    // If we can't advance further, break to avoid infinite loop
    if (startIndex >= text.length - chunkOverlap) {
      break;
    }
  }

  return chunks;
}

// Function to run document ingestion
async function ingestDocuments() {
  try {
    console.log('Starting document ingestion process...');
    
    // Initialize vector database
    const vectorDbService = await getVectorDbService();
    console.log('Vector database initialized');
    
    // Initialize embedding service
    const embeddingService = await getEmbeddingService();
    console.log('Embedding service initialized');
    
    // Find all .docx files recursively
    const files = await findDocxFiles(DOCS_DIR);
    console.log(`Found ${files.length} .docx files in ${DOCS_DIR}`);
    
    // Process each file
    const results = [];
    for (const filePath of files) {
      try {
        const fileName = path.basename(filePath);
        console.log(`Processing ${fileName}...`);
        
        // Extract text from document
        const text = await extractTextFromDocx(filePath);
        console.log(`  Extracted ${text.length} characters`);
        
        // Split text into chunks
        const chunks = chunkText(text);
        console.log(`  Split into ${chunks.length} chunks (size: 4000, overlap: 400)`);
        
        // Create metadata
        const metadata = {
          fileName,
          filePath,
          fileType: path.extname(filePath).toLowerCase(),
          processedAt: new Date().toISOString(),
          chunkCount: chunks.length,
        };
        
        // Generate embeddings
        console.log('  Generating embeddings...');
        const embeddings = await embeddingService.generateEmbeddings(chunks);
        
        // Prepare documents for storage
        const documents = chunks.map((chunk, index) => ({
          id: uuidv4(),
          text: chunk,
          embedding: embeddings[index],
          metadata: {
            ...metadata,
            chunkIndex: index,
          },
        }));
        
        // Store documents in vector database
        console.log('  Storing in vector database...');
        await vectorDbService.storeDocuments(documents);
        
        console.log(`  Successfully processed ${fileName}`);
        results.push({ fileName, chunkCount: chunks.length });
      } catch (error) {
        console.error(`Failed to process ${filePath}:`, error);
      }
    }
    
    // Print summary
    console.log('\nDocument Processing Summary:');
    console.log('----------------------------');
    results.forEach(result => {
      console.log(`${result.fileName}: ${result.chunkCount} chunks`);
    });
    console.log('----------------------------');
    console.log(`Total files processed: ${results.length}`);
    console.log(`Total chunks: ${results.reduce((sum, result) => sum + result.chunkCount, 0)}`);
    
    console.log('Document ingestion completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('Document ingestion failed:', error);
    process.exit(1);
  }
}

// Run the ingestion process
ingestDocuments();
