/**
 * Document Ingestion Script
 * 
 * This script processes documents and stores them in the vector database.
 * It can be run separately from the main server.
 * 
 * Usage: node src/ingest-documents.js [--dir=<directory>] [--force]
 * 
 * Options:
 *   --dir=<directory>  Specify the directory containing documents (default: ./docs)
 *   --force            Force reprocessing of all documents
 */

const { getDocumentIngestionService } = require('./services/document');
const { getVectorDbService } = require('./services/vectordb');

// Parse command line arguments
const args = process.argv.slice(2);
const options = {
  dir: './docs',
  force: false
};

args.forEach(arg => {
  if (arg.startsWith('--dir=')) {
    options.dir = arg.split('=')[1];
  } else if (arg === '--force') {
    options.force = true;
  }
});

// Function to run document ingestion
async function ingestDocuments() {
  try {
    console.log('Starting document ingestion process...');
    console.log(`Directory: ${options.dir}`);
    console.log(`Force reprocessing: ${options.force}`);
    
    // Initialize vector database
    const vectorDbService = await getVectorDbService();
    console.log('Vector database initialized');
    
    // Initialize document ingestion service
    const documentIngestionService = getDocumentIngestionService();
    
    // Override the docs directory if specified
    if (options.dir !== './docs') {
      documentIngestionService.docsDir = options.dir;
    }
    
    // Process documents
    await documentIngestionService.initialize(options.force);
    
    console.log('Document ingestion completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('Document ingestion failed:', error);
    process.exit(1);
  }
}

// Run the ingestion process
ingestDocuments();
