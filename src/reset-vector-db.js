#!/usr/bin/env node

/**
 * Reset Vector Database Collection
 * 
 * This script resets the vector database collection to prepare for
 * ingesting documents with a different embedding model.
 * 
 * Usage: node src/reset-vector-db.js
 */

const { getVectorDbService } = require('./services/vectordb');

async function resetVectorDb() {
  try {
    console.log('Resetting vector database collection...');
    
    // Get vector database service
    const vectorDbService = await getVectorDbService();
    
    // Reset collection
    await vectorDbService.resetCollection();
    
    console.log('Vector database collection reset successfully');
    process.exit(0);
  } catch (error) {
    console.error('Failed to reset vector database collection:', error);
    process.exit(1);
  }
}

// Run the reset process
resetVectorDb();
