const express = require('express');
const chatController = require('../controllers/chatController');
const enhancedChatController = require('../controllers/enhancedChatController');
const documentChatController = require('../controllers/documentChatController');
const documentController = require('../controllers/documentController');

const router = express.Router();

// Chat routes
router.post('/chat', chatController.chat);

// Document routes
router.post('/documents/upload', documentController.uploadDocument);
router.get('/documents', documentController.listDocuments);
router.delete('/documents/:id', documentController.deleteDocument);

module.exports = router;
