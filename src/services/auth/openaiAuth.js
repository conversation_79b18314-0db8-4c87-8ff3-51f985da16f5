const axios = require('axios');
const base64 = require('base-64');
const config = require('../../config');
const retry = require('async-retry');

class OpenAIAuthService {
  constructor() {
    this.clientId = config.auth.openai.clientId;
    this.clientSecret = config.auth.openai.clientSecret;
    this.appKey = config.auth.openai.appKey;
    this.tokenUrl = config.auth.openai.tokenUrl;
    this.azureEndpoint = config.auth.openai.azureEndpoint;
    this.apiVersion = config.auth.openai.apiVersion;

    this.token = null;
    this.tokenExpiry = null;
  }

  /**
   * Get a valid access token, refreshing if necessary
   * @returns {Promise<string>} - The access token
   */
  async getToken() {
    // Check if we have a valid token
    if (this.token && this.tokenExpiry && this.tokenExpiry > Date.now()) {
      return this.token;
    }

    // Otherwise, get a new token with retry logic
    try {
      const value = base64.encode(`${this.clientId}:${this.clientSecret}`);

      // Use retry logic to handle transient errors
      const response = await retry(
        async (bail, attempt) => {
          console.log(`Attempt ${attempt} to get access token...`);

          try {
            return await axios.post(
              this.tokenUrl,
              'grant_type=client_credentials',
              {
                headers: {
                  'Accept': '*/*',
                  'Content-Type': 'application/x-www-form-urlencoded',
                  'Authorization': `Basic ${value}`
                }
              }
            );
          } catch (error) {
            // If it's a 4xx error (except 429), bail immediately as it's likely a configuration issue
            if (error.response && error.response.status >= 400 && error.response.status < 500 && error.response.status !== 429) {
              console.error(`Error getting access token (not retrying): ${error.message}`);
              bail(error);
              return;
            }

            // Otherwise, retry
            console.warn(`Error getting access token. Retrying... Attempt ${attempt}`);
            throw error; // This will trigger a retry
          }
        },
        {
          retries: 3, // Maximum number of retries
          factor: 2, // Exponential backoff factor
          minTimeout: 1000, // Minimum timeout between retries (1 second)
          maxTimeout: 10000, // Maximum timeout between retries (10 seconds)
          randomize: true, // Add randomization to the backoff
          onRetry: (error, attempt) => {
            console.log(`Retrying token request after error: ${error.message}. Attempt ${attempt}`);
          }
        }
      );

      // Save the token and expiry
      this.token = response.data.access_token;
      // Set expiry to slightly before the actual expiry to be safe
      const expiresIn = response.data.expires_in || 3600; // Default to 1 hour if not provided
      this.tokenExpiry = Date.now() + (expiresIn - 60) * 1000; // Subtract 60 seconds for safety

      console.log('Successfully obtained access token');
      console.log('Token response:', {
        tokenLength: this.token ? this.token.length : 0,
        expiresIn: expiresIn,
        expiryTime: new Date(this.tokenExpiry).toISOString()
      });

      return this.token;
    } catch (error) {
      console.error('Failed to get OpenAI access token:', error);
      throw new Error(`Authentication failed: ${error.message}`);
    }
  }

  /**
   * Get authentication details for Azure OpenAI
   * @returns {Promise<Object>} - Authentication details for Azure OpenAI
   */
  async getAzureOpenAIConfig() {
    const token = await this.getToken();

    return {
      azureOpenAI: {
        apiVersion: this.apiVersion,
        endpoint: this.azureEndpoint,
        apiKey: token,
        appKey: this.appKey,
      }
    };
  }
}

// Singleton instance
let openaiAuthService;

/**
 * Get the OpenAI authentication service instance
 * @returns {OpenAIAuthService} - OpenAI authentication service
 */
function getOpenAIAuthService() {
  if (!openaiAuthService) {
    openaiAuthService = new OpenAIAuthService();
  }
  return openaiAuthService;
}

module.exports = {
  getOpenAIAuthService,
};
