const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const mammoth = require('mammoth');
const config = require('../../config');
const { getEmbeddingService } = require('../embedding');
const { getVectorDbService } = require('../vectordb');

/**
 * Service for ingesting documents into the vector database
 */
class DocumentIngestionService {
  constructor() {
    this.docsDir = path.join(process.cwd(), 'docs');
    this.chunkSize = config.document.chunkSize;
    this.chunkOverlap = config.document.chunkOverlap;
  }

  /**
   * Initialize the service and ingest documents
   */
  async initialize() {
    try {
      console.log('Initializing document ingestion service...');

      // Create docs directory if it doesn't exist
      if (!fs.existsSync(this.docsDir)) {
        fs.mkdirSync(this.docsDir, { recursive: true });
        console.log(`Created docs directory at ${this.docsDir}`);
      }

      // Get all .docx files recursively
      const docxFiles = this.findDocxFiles(this.docsDir);
      console.log(`Found ${docxFiles.length} .docx files in ${this.docsDir}`);

      if (docxFiles.length > 0) {
        // Process all documents
        await this.processDocuments(docxFiles);
      }

      console.log('Document ingestion service initialized');
      return true;
    } catch (error) {
      console.error('Failed to initialize document ingestion service:', error);
      // Don't throw error to prevent server startup failure
      return false;
    }
  }

  /**
   * Find all .docx files in a directory and its subdirectories
   * @param {string} dir - Directory to search
   * @returns {string[]} - Array of file paths
   */
  findDocxFiles(dir) {
    const files = [];

    const processDirectory = (currentDir) => {
      const items = fs.readdirSync(currentDir);

      for (const item of items) {
        const itemPath = path.join(currentDir, item);
        const stats = fs.statSync(itemPath);

        if (stats.isDirectory()) {
          // Skip hidden directories (starting with .)
          if (!item.startsWith('.')) {
            files.push(...processDirectory(itemPath));
          }
        } else if (stats.isFile() && item.toLowerCase().endsWith('.docx')) {
          // Skip temporary files (starting with ~$)
          if (!item.startsWith('~$')) {
            files.push(itemPath);
          }
        }
      }

      return files;
    };

    return processDirectory(dir);
  }

  /**
   * Process multiple documents
   * @param {string[]} filePaths - Array of file paths
   */
  async processDocuments(filePaths) {
    try {
      // Get services
      const embeddingService = await getEmbeddingService();
      const vectorDbService = await getVectorDbService();

      // Process each file
      const results = [];
      for (const filePath of filePaths) {
        try {
          const result = await this.processDocument(filePath, embeddingService, vectorDbService);
          results.push(result);
        } catch (error) {
          console.error(`Failed to process ${filePath}:`, error);
        }
      }

      // Print summary
      console.log('\nDocument Processing Summary:');
      console.log('----------------------------');
      results.forEach(result => {
        console.log(`${result.fileName}: ${result.chunkCount} chunks`);
      });
      console.log('----------------------------');
      console.log(`Total files processed: ${results.length}`);
      console.log(`Total chunks: ${results.reduce((sum, result) => sum + result.chunkCount, 0)}`);

      return results;
    } catch (error) {
      console.error('Failed to process documents:', error);
      throw error;
    }
  }

  /**
   * Process a single document
   * @param {string} filePath - Path to the document
   * @param {Object} embeddingService - Embedding service
   * @param {Object} vectorDbService - Vector database service
   */
  async processDocument(filePath, embeddingService, vectorDbService) {
    try {
      const fileName = path.basename(filePath);
      console.log(`Processing ${fileName}...`);

      // Extract text from document
      const text = await this.extractTextFromDocx(filePath);
      console.log(`  Extracted ${text.length} characters`);

      // Split text into chunks
      const chunks = this.chunkText(text);
      console.log(`  Split into ${chunks.length} chunks (size: ${this.chunkSize}, overlap: ${this.chunkOverlap})`);

      // Create metadata
      const metadata = {
        fileName,
        filePath,
        fileType: path.extname(filePath).toLowerCase(),
        processedAt: new Date().toISOString(),
        chunkCount: chunks.length,
      };

      // Generate embeddings
      console.log('  Generating embeddings...');
      const embeddings = await embeddingService.generateEmbeddings(chunks);

      // Prepare documents for storage
      const documents = chunks.map((chunk, index) => ({
        id: uuidv4(),
        text: chunk,
        embedding: embeddings[index],
        metadata: {
          ...metadata,
          chunkIndex: index,
        },
      }));

      // Store documents in vector database
      console.log('  Storing in vector database...');
      await vectorDbService.storeDocuments(documents);

      console.log(`  Successfully processed ${fileName}`);
      return { fileName, chunkCount: chunks.length };
    } catch (error) {
      console.error(`Error processing ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Extract text from a .docx file
   * @param {string} filePath - Path to the .docx file
   * @returns {Promise<string>} - Extracted text
   */
  async extractTextFromDocx(filePath) {
    try {
      const result = await mammoth.extractRawText({ path: filePath });
      return result.value;
    } catch (error) {
      console.error(`Error extracting text from ${filePath}:`, error);
      throw new Error(`Failed to extract text from document: ${error.message}`);
    }
  }

  /**
   * Split text into chunks with overlap
   * @param {string} text - Text to chunk
   * @returns {string[]} - Array of text chunks
   */
  chunkText(text) {
    const chunks = [];
    let startIndex = 0;

    while (startIndex < text.length) {
      const endIndex = Math.min(startIndex + this.chunkSize, text.length);
      chunks.push(text.slice(startIndex, endIndex));
      startIndex = endIndex - this.chunkOverlap;

      // If we can't advance further, break to avoid infinite loop
      if (startIndex >= text.length - this.chunkOverlap) {
        break;
      }
    }

    return chunks;
  }
}

// Singleton instance
let documentIngestionService;

/**
 * Get the document ingestion service instance
 * @returns {DocumentIngestionService} - Document ingestion service
 */
function getDocumentIngestionService() {
  if (!documentIngestionService) {
    documentIngestionService = new DocumentIngestionService();
  }
  return documentIngestionService;
}

module.exports = {
  getDocumentIngestionService,
};
