/**
 * Document Search Service
 * 
 * This service provides functions to search through extracted document content
 * without requiring vector embeddings.
 */

const fs = require('fs');
const path = require('path');

class DocumentSearchService {
  constructor() {
    this.extractedDir = path.join(process.cwd(), 'src', 'data', 'extracted');
    this.metadata = this.loadMetadata();
    this.documents = this.loadDocuments();
  }

  /**
   * Load document metadata
   * @returns {Array} Array of document metadata objects
   */
  loadMetadata() {
    try {
      const metadataPath = path.join(this.extractedDir, 'metadata.json');
      if (fs.existsSync(metadataPath)) {
        return JSON.parse(fs.readFileSync(metadataPath, 'utf8'));
      }
      return [];
    } catch (error) {
      console.error('Error loading document metadata:', error);
      return [];
    }
  }

  /**
   * Load all document content
   * @returns {Array} Array of document objects with content
   */
  loadDocuments() {
    try {
      return this.metadata.map(meta => {
        try {
          const content = fs.readFileSync(meta.outputPath, 'utf8');
          return {
            id: path.basename(meta.outputPath, '.txt'),
            text: content,
            metadata: {
              fileName: meta.fileName,
              filePath: meta.filePath,
              extractedAt: meta.extractedAt,
              characters: meta.characters
            }
          };
        } catch (error) {
          console.error(`Error loading document ${meta.fileName}:`, error);
          return null;
        }
      }).filter(doc => doc !== null);
    } catch (error) {
      console.error('Error loading documents:', error);
      return [];
    }
  }

  /**
   * Search for documents relevant to a query using keyword matching
   * @param {string} query - The search query
   * @param {number} limit - Maximum number of results to return
   * @returns {Array} Array of relevant documents
   */
  searchByKeywords(query, limit = 5) {
    // Normalize query
    const normalizedQuery = query.toLowerCase();
    
    // Extract keywords (words with 4+ characters)
    const keywords = normalizedQuery
      .split(/\s+/)
      .filter(word => word.length >= 4)
      .map(word => word.replace(/[^\w]/g, ''));
    
    if (keywords.length === 0) {
      return [];
    }
    
    // Score documents based on keyword matches
    const scoredDocs = this.documents.map(doc => {
      const normalizedText = doc.text.toLowerCase();
      
      // Calculate score based on keyword frequency
      let score = 0;
      let matches = 0;
      
      keywords.forEach(keyword => {
        // Count occurrences of the keyword
        const regex = new RegExp(`\\b${keyword}\\w*\\b`, 'gi');
        const occurrences = (normalizedText.match(regex) || []).length;
        
        if (occurrences > 0) {
          matches++;
          score += occurrences;
        }
      });
      
      // Boost score if document title contains keywords
      const fileName = doc.metadata.fileName.toLowerCase();
      keywords.forEach(keyword => {
        if (fileName.includes(keyword)) {
          score += 5;
        }
      });
      
      // Normalize score by document length to avoid favoring longer documents too much
      const normalizedScore = score / Math.sqrt(doc.text.length / 500);
      
      // Only include documents with at least one keyword match
      return {
        ...doc,
        score: matches > 0 ? normalizedScore : 0
      };
    });
    
    // Filter out documents with no matches and sort by score
    return scoredDocs
      .filter(doc => doc.score > 0)
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);
  }

  /**
   * Search for documents specifically about SAP BO in Citrix
   * @param {number} limit - Maximum number of results to return
   * @returns {Array} Array of relevant documents
   */
  searchBoCitrix(limit = 3) {
    // Find documents specifically about BO in Citrix
    const boCitrixDocs = this.documents.filter(doc => {
      const fileName = doc.metadata.fileName.toLowerCase();
      const text = doc.text.toLowerCase();
      
      return (
        (fileName.includes('citrix') || text.includes('citrix')) &&
        (fileName.includes('bo') || text.includes('bo') || 
         fileName.includes('business objects') || text.includes('business objects'))
      );
    });
    
    // Sort by relevance (simple heuristic: documents with "citrix" in the title are more relevant)
    return boCitrixDocs
      .sort((a, b) => {
        const aHasCitrixInTitle = a.metadata.fileName.toLowerCase().includes('citrix') ? 1 : 0;
        const bHasCitrixInTitle = b.metadata.fileName.toLowerCase().includes('citrix') ? 1 : 0;
        
        if (aHasCitrixInTitle !== bHasCitrixInTitle) {
          return bHasCitrixInTitle - aHasCitrixInTitle;
        }
        
        // If both have or don't have "citrix" in the title, sort by document length (shorter first)
        return a.text.length - b.text.length;
      })
      .slice(0, limit)
      .map(doc => ({
        ...doc,
        score: 1.0 // Assign a score of 1.0 to all matches
      }));
  }

  /**
   * Search for documents relevant to a query
   * @param {string} query - The search query
   * @param {number} limit - Maximum number of results to return
   * @returns {Array} Array of relevant documents
   */
  search(query, limit = 5) {
    // Check if query is about SAP BO in Citrix
    const normalizedQuery = query.toLowerCase();
    const isBoCitrixQuery = 
      (normalizedQuery.includes('sap bo') || normalizedQuery.includes('business objects')) && 
      (normalizedQuery.includes('citrix') || normalizedQuery.includes('access'));
    
    if (isBoCitrixQuery) {
      return this.searchBoCitrix(limit);
    }
    
    // Otherwise, use keyword search
    return this.searchByKeywords(query, limit);
  }
}

// Singleton instance
let instance = null;

/**
 * Get the document search service instance
 * @returns {Promise<DocumentSearchService>} Document search service instance
 */
async function getDocumentSearchService() {
  if (!instance) {
    instance = new DocumentSearchService();
  }
  return instance;
}

module.exports = {
  getDocumentSearchService
};
