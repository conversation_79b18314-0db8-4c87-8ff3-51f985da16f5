const { AzureOpenAI } = require('openai');
const base64 = require('base-64');
const axios = require('axios');
const config = require('../../config');

/**
 * Azure OpenAI Embedding Service
 * 
 * This service integrates with Azure OpenAI using Cisco OAuth token-based authentication
 * to generate embeddings for text.
 */
class AzureOpenAIEmbeddingService {
  constructor() {
    this.clientId = config.llm.azureOpenai.clientId;
    this.clientSecret = config.llm.azureOpenai.clientSecret;
    this.appKey = config.llm.azureOpenai.appKey;
    this.tokenUrl = config.llm.azureOpenai.tokenUrl;
    this.azureEndpoint = config.llm.azureOpenai.azureEndpoint;
    this.apiVersion = config.llm.azureOpenai.apiVersion;
    this.deploymentName = config.llm.azureOpenai.deploymentName;
    
    // Token cache for performance and to avoid unnecessary auth requests
    this.token = null;
    this.tokenExpiry = null;
  }

  /**
   * Get a valid access token, refreshing if necessary
   * @returns {Promise<string>} - The access token
   */
  async getToken() {
    // Check if we have a valid token
    if (this.token && this.tokenExpiry && this.tokenExpiry > Date.now()) {
      return this.token;
    }

    try {
      // Create Base64 encoded authorization value
      const value = base64.encode(`${this.clientId}:${this.clientSecret}`);
      
      // Make the token request
      const response = await axios.post(
        this.tokenUrl,
        'grant_type=client_credentials',
        {
          headers: {
            'Accept': '*/*',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Authorization': `Basic ${value}`
          }
        }
      );
      
      // Save the token and expiry
      this.token = response.data.access_token;
      // Set expiry to slightly before the actual expiry to be safe
      const expiresIn = response.data.expires_in || 3600; // Default to 1 hour if not provided
      this.tokenExpiry = Date.now() + (expiresIn - 60) * 1000; // Subtract 60 seconds for safety
      
      return this.token;
    } catch (error) {
      console.error('Failed to get Azure OpenAI access token:', error);
      throw new Error(`Authentication failed: ${error.message}`);
    }
  }

  /**
   * Initialize the Azure OpenAI client
   * @returns {Promise<AzureOpenAI>} - The Azure OpenAI client
   */
  async getClient() {
    const token = await this.getToken();
    
    // The AzureOpenAI client requires the apiKey parameter to be set
    // We use the token as the API key
    return new AzureOpenAI({
      azure_endpoint: this.azureEndpoint,
      apiKey: token, // Use apiKey instead of api_key
      api_version: this.apiVersion
    });
  }

  /**
   * Generate embeddings for a text or array of texts
   * @param {string|string[]} texts - Text or array of texts to embed
   * @returns {Promise<number[]|number[][]>} - Embedding vector(s)
   */
  async generateEmbeddings(texts) {
    try {
      const client = await this.getClient();
      
      const isArray = Array.isArray(texts);
      const inputTexts = isArray ? texts : [texts];
      
      const response = await client.embeddings.create({
        model: 'text-embedding-ada-002', // Azure OpenAI embedding model
        input: inputTexts,
        user: JSON.stringify({ appkey: this.appKey }),
        extra_headers: {
          'app-key': this.appKey
        }
      });
      
      const embeddings = response.data.map(item => item.embedding);
      return isArray ? embeddings : embeddings[0];
    } catch (error) {
      console.error('Failed to generate embeddings:', error);
      throw new Error(`Embedding generation failed: ${error.message}`);
    }
  }
}

module.exports = AzureOpenAIEmbeddingService;
