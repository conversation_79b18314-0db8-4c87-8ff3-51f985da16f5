const { pipeline } = require('@xenova/transformers');
const config = require('../../config');

class HuggingFaceEmbeddingService {
  constructor() {
    this.model = config.embedding.huggingface.model;
    this.pipeline = null;
  }

  /**
   * Initialize the embedding pipeline
   */
  async initialize() {
    if (!this.pipeline) {
      this.pipeline = await pipeline('feature-extraction', this.model);
      console.log(`HuggingFace embedding model '${this.model}' loaded`);
    }
  }

  /**
   * Generate embeddings for a text or array of texts
   * @param {string|string[]} texts - Text or array of texts to embed
   * @returns {Promise<number[]|number[][]>} - Embedding vector(s)
   */
  async generateEmbeddings(texts) {
    try {
      await this.initialize();
      
      const isArray = Array.isArray(texts);
      const inputTexts = isArray ? texts : [texts];
      
      const embeddings = await Promise.all(
        inputTexts.map(async (text) => {
          const result = await this.pipeline(text, { pooling: 'mean', normalize: true });
          return Array.from(result.data);
        })
      );
      
      return isArray ? embeddings : embeddings[0];
    } catch (error) {
      console.error('Failed to generate embeddings:', error);
      throw new Error(`Embedding generation failed: ${error.message}`);
    }
  }
}

module.exports = HuggingFaceEmbeddingService;
