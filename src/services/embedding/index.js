const config = require('../../config');
const OpenAIEmbeddingService = require('./openaiEmbedding');
const HuggingFaceEmbeddingService = require('./huggingfaceEmbedding');
const AzureOpenAIEmbeddingService = require('./azureOpenaiEmbedding');
const MistralEmbeddingService = require('./mistralEmbedding');

/**
 * Factory function to create an embedding service based on configuration
 * @returns {Object} - Embedding service instance
 */
function createEmbeddingService() {
  const provider = config.embedding.provider;

  switch (provider) {
    case 'openai':
      return new OpenAIEmbeddingService();
    case 'azure-openai':
      return new AzureOpenAIEmbeddingService();
    case 'huggingface':
      return new HuggingFaceEmbeddingService();
    case 'mistral':
      return new MistralEmbeddingService();
    // Add other embedding providers here
    default:
      throw new Error(`Unsupported embedding provider: ${provider}`);
  }
}

// Create the embedding service
let embeddingService;

async function getEmbeddingService() {
  if (!embeddingService) {
    embeddingService = createEmbeddingService();
    // Initialize if needed (e.g., for HuggingFace)
    if (embeddingService.initialize) {
      await embeddingService.initialize();
    }
  }
  return embeddingService;
}

module.exports = {
  getEmbeddingService,
};
