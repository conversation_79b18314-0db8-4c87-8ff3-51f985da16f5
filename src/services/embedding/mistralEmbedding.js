const axios = require('axios');
const config = require('../../config');

/**
 * Utility function to sleep for a specified number of milliseconds
 * @param {number} ms - Milliseconds to sleep
 * @returns {Promise<void>}
 */
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * Mistral Embedding Service
 *
 * This service integrates with Mistral AI's embedding API to generate
 * embeddings for text.
 */
class MistralEmbeddingService {
  constructor() {
    this.apiKey = config.embedding.mistral.apiKey;
    this.model = config.embedding.mistral.model || 'mistral-embed';
    this.apiUrl = config.embedding.mistral.apiUrl || 'https://api.mistral.ai/v1/embeddings';
    this.maxRetries = 5;
    this.batchSize = 5; // Process texts in smaller batches
    this.rateLimitDelay = 1000; // 1 second delay between API calls
  }

  /**
   * Generate embeddings for a text or array of texts with rate limiting and retries
   * @param {string|string[]} texts - Text or array of texts to embed
   * @returns {Promise<number[]|number[][]>} - Embedding vector(s)
   */
  async generateEmbeddings(texts) {
    const isArray = Array.isArray(texts);
    const inputTexts = isArray ? texts : [texts];

    // Process in batches to avoid rate limits
    const allEmbeddings = [];

    // Split texts into smaller batches
    for (let i = 0; i < inputTexts.length; i += this.batchSize) {
      const batch = inputTexts.slice(i, i + this.batchSize);
      console.log(`Processing batch ${i / this.batchSize + 1} of ${Math.ceil(inputTexts.length / this.batchSize)}`);

      try {
        // Add delay between batches to avoid rate limits
        if (i > 0) {
          console.log(`Waiting ${this.rateLimitDelay}ms before next batch...`);
          await sleep(this.rateLimitDelay);
        }

        const batchEmbeddings = await this.generateBatchEmbeddings(batch);
        allEmbeddings.push(...batchEmbeddings);
      } catch (error) {
        console.error(`Error processing batch ${i / this.batchSize + 1}:`, error);
        throw error;
      }
    }

    return isArray ? allEmbeddings : allEmbeddings[0];
  }

  /**
   * Generate embeddings for a batch of texts with retry logic
   * @param {string[]} batch - Batch of texts to embed
   * @returns {Promise<number[][]>} - Embedding vectors
   */
  async generateBatchEmbeddings(batch) {
    let retries = 0;
    let backoffTime = 1000; // Start with 1 second

    while (retries < this.maxRetries) {
      try {
        const response = await axios.post(
          this.apiUrl,
          {
            model: this.model,
            input: batch,
          },
          {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${this.apiKey}`
            }
          }
        );

        return response.data.data.map(item => item.embedding);
      } catch (error) {
        if (error.response && error.response.status === 429) {
          retries++;

          // Get retry-after header or use exponential backoff
          const retryAfter = error.response.headers['retry-after']
            ? parseInt(error.response.headers['retry-after']) * 1000
            : backoffTime;

          console.warn(`Rate limit exceeded. Retrying in ${retryAfter/1000} seconds... (Attempt ${retries}/${this.maxRetries})`);

          // Wait before retrying
          await sleep(retryAfter);

          // Exponential backoff with jitter
          backoffTime = Math.min(backoffTime * 2, 60000) + Math.random() * 1000;
        } else {
          console.error('Failed to generate embeddings with Mistral AI:', error);
          if (error.response) {
            console.error('Response data:', error.response.data);
            console.error('Response status:', error.response.status);
          }
          throw new Error(`Mistral embedding generation failed: ${error.message}`);
        }
      }
    }

    throw new Error(`Failed to generate embeddings after ${this.maxRetries} retries due to rate limiting`);
  }
}

module.exports = MistralEmbeddingService;
