const { OpenAI } = require('openai');
const config = require('../../config');

class OpenAIEmbeddingService {
  constructor() {
    this.client = new OpenAI({
      apiKey: config.embedding.openai.apiKey,
    });
    this.model = config.embedding.openai.model;
  }

  /**
   * Generate embeddings for a text or array of texts
   * @param {string|string[]} texts - Text or array of texts to embed
   * @returns {Promise<number[]|number[][]>} - Embedding vector(s)
   */
  async generateEmbeddings(texts) {
    try {
      const isArray = Array.isArray(texts);
      const inputTexts = isArray ? texts : [texts];
      
      const response = await this.client.embeddings.create({
        model: this.model,
        input: inputTexts,
      });
      
      const embeddings = response.data.map(item => item.embedding);
      return isArray ? embeddings : embeddings[0];
    } catch (error) {
      console.error('Failed to generate embeddings:', error);
      throw new Error(`Embedding generation failed: ${error.message}`);
    }
  }
}

module.exports = OpenAIEmbeddingService;
