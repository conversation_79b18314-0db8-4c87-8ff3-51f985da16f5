const config = require('../../config');

class AnthropicLLMService {
  constructor() {
    this.apiKey = config.llm.anthropic.apiKey;
    this.model = config.llm.anthropic.model;
  }

  /**
   * Generate a response from the LLM
   * @param {string} prompt - The user's prompt
   * @param {Array} context - Array of context documents
   * @returns {Promise<string>} - The generated response
   */
  async generateResponse(prompt, context = []) {
    try {
      // Prepare system message with context
      let systemPrompt = 'You are a helpful assistant that answers questions based on the provided context.';

      if (context.length > 0) {
        systemPrompt += '\n\nContext information is below:\n';

        // Add context with source information
        context.forEach((doc, index) => {
          systemPrompt += `\n---\nSource ${index + 1}: ${doc.metadata?.fileName || 'Unknown'}\n${doc.text}\n---`;
        });

        systemPrompt += '\n\nGiven the context information and not prior knowledge, answer the question.';
        systemPrompt += '\n\nIf you use information from the sources, include a reference to the source at the end of the relevant sentence or paragraph in the format [Source X], where X is the source number.';
        systemPrompt += '\n\nIf the answer cannot be found in the provided context, say so and answer based on your general knowledge.';
      }

      // Note: This is a simplified implementation. In a real application,
      // you would use the Anthropic API client library.
      const response = await fetch('https://api.anthropic.com/v1/messages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': this.apiKey,
          'anthropic-version': '2023-06-01',
        },
        body: JSON.stringify({
          model: this.model,
          system: systemPrompt,
          messages: [
            { role: 'user', content: prompt }
          ],
          max_tokens: 1000,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(`Anthropic API error: ${error.error?.message || 'Unknown error'}`);
      }

      const data = await response.json();
      return data.content[0].text;
    } catch (error) {
      console.error('Failed to generate response:', error);
      throw new Error(`Response generation failed: ${error.message}`);
    }
  }
}

module.exports = AnthropicLLMService;
