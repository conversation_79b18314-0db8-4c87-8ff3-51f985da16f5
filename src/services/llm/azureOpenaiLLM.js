const { AzureOpenAI } = require('openai');
const base64 = require('base-64');
const axios = require('axios');
const config = require('../../config');

/**
 * Azure OpenAI LLM Service
 *
 * This service integrates with Azure OpenAI using Cisco OAuth token-based authentication.
 * It handles:
 * 1. OAuth token acquisition and caching
 * 2. Azure OpenAI client initialization
 * 3. Chat completion requests with proper formatting
 *
 * The service requires the following configuration:
 * - clientId: Cisco OAuth client ID
 * - clientSecret: Cisco OAuth client secret
 * - appKey: App key for Azure OpenAI access
 * - tokenUrl: Cisco OAuth token endpoint
 * - azureEndpoint: Azure OpenAI API endpoint
 * - apiVersion: Azure OpenAI API version
 * - deploymentName: Azure OpenAI deployment name (e.g., gpt-4o)
 */
class AzureOpenAILLMService {
  /**
   * Initialize the Azure OpenAI LLM service
   *
   * Loads configuration from the config object and initializes token cache.
   * No API calls are made during initialization.
   */
  constructor() {
    this.clientId = config.llm.azureOpenai.clientId;
    this.clientSecret = config.llm.azureOpenai.clientSecret;
    this.appKey = config.llm.azureOpenai.appKey;
    this.tokenUrl = config.llm.azureOpenai.tokenUrl;
    this.azureEndpoint = config.llm.azureOpenai.azureEndpoint;
    this.apiVersion = config.llm.azureOpenai.apiVersion;
    this.deploymentName = config.llm.azureOpenai.deploymentName;

    // Token cache for performance and to avoid unnecessary auth requests
    this.token = null;
    this.tokenExpiry = null;
  }

  /**
   * Get a valid access token, refreshing if necessary
   * @returns {Promise<string>} - The access token
   */
  async getToken() {
    // Check if we have a valid token
    if (this.token && this.tokenExpiry && this.tokenExpiry > Date.now()) {
      return this.token;
    }

    try {
      // Create Base64 encoded authorization value
      const value = base64.encode(`${this.clientId}:${this.clientSecret}`);

      // Make the token request
      const response = await axios.post(
        this.tokenUrl,
        'grant_type=client_credentials',
        {
          headers: {
            'Accept': '*/*',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Authorization': `Basic ${value}`
          }
        }
      );

      // Save the token and expiry
      this.token = response.data.access_token;
      // Set expiry to slightly before the actual expiry to be safe
      const expiresIn = response.data.expires_in || 3600; // Default to 1 hour if not provided
      this.tokenExpiry = Date.now() + (expiresIn - 60) * 1000; // Subtract 60 seconds for safety

      return this.token;
    } catch (error) {
      console.error('Failed to get Azure OpenAI access token:', error);
      throw new Error(`Authentication failed: ${error.message}`);
    }
  }

  /**
   * Initialize the Azure OpenAI client
   * @returns {Promise<AzureOpenAI>} - The Azure OpenAI client
   */
  async getClient() {
    const token = await this.getToken();

    // The AzureOpenAI client requires the apiKey parameter to be set
    // We use the token as the API key
    return new AzureOpenAI({
      azure_endpoint: this.azureEndpoint,
      apiKey: token, // Use apiKey instead of api_key
      api_version: this.apiVersion
    });
  }

  /**
   * Generate a response from the LLM
   * @param {string} prompt - The user's prompt
   * @param {Array} context - Array of context documents
   * @returns {Promise<string>} - The generated response
   */
  async generateResponse(prompt, context = []) {
    try {
      // Get the Azure OpenAI client
      const client = await this.getClient();

      // Prepare system message with context
      let systemMessage = 'You are a helpful assistant that answers questions based on the provided context.';

      if (context.length > 0) {
        systemMessage += '\n\nContext information is below:\n';

        // Add context with source information
        context.forEach((doc, index) => {
          systemMessage += `\n---\nSource ${index + 1}: ${doc.metadata?.fileName || 'Unknown'}\n${doc.text}\n---`;
        });

        systemMessage += '\n\nGiven the context information and not prior knowledge, answer the question.';
        systemMessage += '\n\nIf you use information from the sources, include a reference to the source at the end of the relevant sentence or paragraph in the format [Source X], where X is the source number.';
        systemMessage += '\n\nIf the answer cannot be found in the provided context, say so and answer based on your general knowledge.';
      }

      // Define the messages
      const messages = [
        { role: 'system', content: systemMessage },
        { role: 'user', content: prompt }
      ];

      // Make the chat completion request
      const response = await client.chat.completions.create({
        model: this.deploymentName,
        messages: messages,
        temperature: 0.7,
        max_tokens: 1000,
        user: JSON.stringify({ appkey: this.appKey }),  // Format exactly as required by the API
        extra_headers: {
          'app-key': this.appKey
        }
      });

      return response.choices[0].message.content;
    } catch (error) {
      console.error('Failed to generate response from Azure OpenAI:', error);
      throw new Error(`Azure OpenAI response generation failed: ${error.message}`);
    }
  }
}

module.exports = AzureOpenAILLMService;
