const config = require('../../config');
const OpenAILLMService = require('./openaiLLM');
const AzureOpenAILLMService = require('./azureOpenaiLLM');
const AnthropicLLMService = require('./anthropicLLM');

/**
 * Factory function to create an LLM service based on configuration
 * @returns {Object} - LLM service instance
 */
function createLLMService() {
  const provider = config.llm.provider;

  switch (provider) {
    case 'openai':
      return new OpenAILLMService();
    case 'azure-openai':
      return new AzureOpenAILLMService();
    case 'anthropic':
      return new AnthropicLLMService();
    // Add other LLM providers here
    default:
      throw new Error(`Unsupported LLM provider: ${provider}`);
  }
}

// Create the LLM service
let llmService;

function getLLMService() {
  if (!llmService) {
    llmService = createLLMService();
  }
  return llmService;
}

module.exports = {
  getLLMService,
};
