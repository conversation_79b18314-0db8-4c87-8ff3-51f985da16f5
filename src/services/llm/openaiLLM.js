const { OpenAI } = require('openai');
const config = require('../../config');

class OpenAILLMService {
  constructor() {
    this.client = new OpenAI({
      apiKey: config.llm.openai.apiKey,
    });
    this.model = config.llm.openai.model;
  }

  /**
   * Generate a response from the LLM
   * @param {string} prompt - The user's prompt
   * @param {Array} context - Array of context documents
   * @returns {Promise<string>} - The generated response
   */
  async generateResponse(prompt, context = []) {
    try {
      // Prepare system message with context
      let systemMessage = 'You are a helpful assistant that answers questions based on the provided context.';

      if (context.length > 0) {
        systemMessage += '\n\nContext information is below:\n';

        // Add context with source information
        context.forEach((doc, index) => {
          systemMessage += `\n---\nSource ${index + 1}: ${doc.metadata?.fileName || 'Unknown'}\n${doc.text}\n---`;
        });

        systemMessage += '\n\nGiven the context information and not prior knowledge, answer the question.';
        systemMessage += '\n\nIf you use information from the sources, include a reference to the source at the end of the relevant sentence or paragraph in the format [Source X], where X is the source number.';
        systemMessage += '\n\nIf the answer cannot be found in the provided context, say so and answer based on your general knowledge.';
      }

      const response = await this.client.chat.completions.create({
        model: this.model,
        messages: [
          { role: 'system', content: systemMessage },
          { role: 'user', content: prompt }
        ],
        temperature: 0.7,
        max_tokens: 1000,
      });

      return response.choices[0].message.content;
    } catch (error) {
      console.error('Failed to generate response:', error);
      throw new Error(`Response generation failed: ${error.message}`);
    }
  }
}

module.exports = OpenAILLMService;
