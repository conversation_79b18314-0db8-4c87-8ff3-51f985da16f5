const config = require('../../config');
const QdrantService = require('./qdrantService');

/**
 * Factory function to create a vector database service based on configuration
 * @returns {Object} - Vector database service instance
 */
function createVectorDbService() {
  const provider = config.vectorDb.provider;
  
  switch (provider) {
    case 'qdrant':
      return new QdrantService();
    // Add other vector DB providers here
    default:
      throw new Error(`Unsupported vector database provider: ${provider}`);
  }
}

// Create and initialize the vector database service
let vectorDbService;

async function getVectorDbService() {
  if (!vectorDbService) {
    vectorDbService = createVectorDbService();
    await vectorDbService.initialize();
  }
  return vectorDbService;
}

module.exports = {
  getVectorDbService,
};
