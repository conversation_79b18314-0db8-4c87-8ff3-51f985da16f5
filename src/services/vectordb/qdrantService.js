const { QdrantClient } = require('@qdrant/js-client-rest');
const config = require('../../config');

class QdrantService {
  constructor() {
    this.client = new QdrantClient({ url: config.vectorDb.qdrant.url });
    this.collectionName = config.vectorDb.qdrant.collection;
    this.dimension = config.vectorDb.dimension;
  }

  /**
   * Initialize the vector database
   */
  async initialize() {
    try {
      // Check if collection exists
      const collections = await this.client.getCollections();
      const collectionExists = collections.collections.some(
        (collection) => collection.name === this.collectionName
      );

      if (!collectionExists) {
        await this.createCollection();
      }

      console.log(`Qdrant collection '${this.collectionName}' is ready`);
      return true;
    } catch (error) {
      console.error('Failed to initialize Qdrant:', error);
      throw new Error(`Qdrant initialization failed: ${error.message}`);
    }
  }

  /**
   * Create a new collection
   */
  async createCollection() {
    try {
      await this.client.createCollection(this.collectionName, {
        vectors: {
          size: this.dimension,
          distance: 'Cosine',
        },
      });
      console.log(`Created collection '${this.collectionName}'`);
    } catch (error) {
      console.error('Failed to create collection:', error);
      throw new Error(`Collection creation failed: ${error.message}`);
    }
  }

  /**
   * Store document chunks with their embeddings
   * @param {Array<{id: string, text: string, embedding: number[], metadata: Object}>} documents
   */
  async storeDocuments(documents) {
    try {
      const points = documents.map((doc) => ({
        id: doc.id,
        vector: doc.embedding,
        payload: {
          text: doc.text,
          ...doc.metadata,
        },
      }));

      await this.client.upsert(this.collectionName, {
        points: points
      });
      return true;
    } catch (error) {
      console.error('Failed to store documents:', error);
      throw new Error(`Document storage failed: ${error.message}`);
    }
  }

  /**
   * Search for similar documents
   * @param {number[]} queryEmbedding - The embedding of the query
   * @param {number} limit - Maximum number of results
   * @returns {Promise<Array>} - Array of matching documents
   */
  async searchSimilar(queryEmbedding, limit = 5) {
    try {
      const response = await this.client.search(this.collectionName, {
        vector: queryEmbedding,
        limit,
        with_payload: true
      });

      return response.map((result) => ({
        id: result.id,
        text: result.payload.text,
        score: result.score,
        metadata: { ...result.payload, text: undefined },
      }));
    } catch (error) {
      console.error('Failed to search documents:', error);
      throw new Error(`Document search failed: ${error.message}`);
    }
  }

  /**
   * Delete a document by ID
   * @param {string} id - Document ID
   */
  async deleteDocument(id) {
    try {
      await this.client.delete(this.collectionName, {
        points: [id]
      });
      return true;
    } catch (error) {
      console.error(`Failed to delete document ${id}:`, error);
      throw new Error(`Document deletion failed: ${error.message}`);
    }
  }

  /**
   * List all documents
   * @param {number} limit - Maximum number of results
   * @param {number} offset - Offset for pagination
   * @returns {Promise<Array>} - Array of documents
   */
  async listDocuments(limit = 100, offset = 0) {
    try {
      const response = await this.client.scroll(this.collectionName, {
        limit,
        offset,
        with_payload: true,
        with_vectors: false
      });

      return response.points.map((point) => ({
        id: point.id,
        text: point.payload.text,
        metadata: { ...point.payload, text: undefined },
      }));
    } catch (error) {
      console.error('Failed to list documents:', error);
      throw new Error(`Document listing failed: ${error.message}`);
    }
  }

  /**
   * Reset the collection by deleting and recreating it
   * @returns {Promise<boolean>} - True if successful
   */
  async resetCollection() {
    try {
      console.log(`Deleting collection '${this.collectionName}'...`);

      // Check if collection exists before trying to delete it
      const collections = await this.client.getCollections();
      const collectionExists = collections.collections.some(
        (collection) => collection.name === this.collectionName
      );

      if (collectionExists) {
        await this.client.deleteCollection(this.collectionName);
        console.log(`Collection '${this.collectionName}' deleted`);
      } else {
        console.log(`Collection '${this.collectionName}' does not exist, skipping deletion`);
      }

      // Create a new collection
      await this.createCollection();

      return true;
    } catch (error) {
      console.error('Failed to reset collection:', error);
      throw new Error(`Collection reset failed: ${error.message}`);
    }
  }
}

module.exports = QdrantService;
