const fs = require('fs');
const path = require('path');
const mammoth = require('mammoth');
const config = require('../config');

/**
 * Extract text from a .docx file
 * @param {string} filePath - Path to the .docx file
 * @returns {Promise<string>} - Extracted text
 */
async function extractTextFromDocx(filePath) {
  try {
    const result = await mammoth.extractRawText({ path: filePath });
    return result.value;
  } catch (error) {
    console.error(`Error extracting text from ${filePath}:`, error);
    throw new Error(`Failed to extract text from document: ${error.message}`);
  }
}

/**
 * Split text into chunks with overlap
 * @param {string} text - Text to chunk
 * @param {number} chunkSize - Size of each chunk
 * @param {number} overlap - Overlap between chunks
 * @returns {string[]} - Array of text chunks
 */
function chunkText(text, chunkSize = config.document.chunkSize, overlap = config.document.chunkOverlap) {
  const chunks = [];
  let startIndex = 0;

  while (startIndex < text.length) {
    const endIndex = Math.min(startIndex + chunkSize, text.length);
    chunks.push(text.slice(startIndex, endIndex));
    startIndex = endIndex - overlap;
    
    // If we can't advance further, break to avoid infinite loop
    if (startIndex >= text.length - overlap) {
      break;
    }
  }

  return chunks;
}

/**
 * Process a document file and return chunks
 * @param {string} filePath - Path to the document file
 * @returns {Promise<{chunks: string[], metadata: Object}>} - Processed document chunks and metadata
 */
async function processDocument(filePath) {
  const fileExt = path.extname(filePath).toLowerCase();
  const fileName = path.basename(filePath);
  
  let text;
  if (fileExt === '.docx') {
    text = await extractTextFromDocx(filePath);
  } else {
    throw new Error(`Unsupported file format: ${fileExt}`);
  }
  
  const chunks = chunkText(text);
  
  const metadata = {
    fileName,
    filePath,
    fileType: fileExt,
    processedAt: new Date().toISOString(),
    chunkCount: chunks.length,
  };
  
  return { chunks, metadata };
}

module.exports = {
  extractTextFromDocx,
  chunkText,
  processDocument,
};
